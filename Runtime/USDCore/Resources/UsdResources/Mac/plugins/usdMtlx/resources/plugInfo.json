# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdMtlxDiscoveryPlugin": {
                        "bases": [
                            "NdrDiscoveryPlugin"
                        ], 
                        "displayName": "MaterialX Discovery"
                    }, 
                    "UsdMtlxFileFormat": {
                        "bases": [
                            "SdfFileFormat"
                        ], 
                        "displayName": "USD MaterialX File Format", 
                        "extensions": [
                            "mtlx"
                        ], 
                        "formatId": "mtlx", 
                        "primary": true, 
                        "supportsEditing": false, 
                        "supportsWriting": false, 
                        "target": "usd"
                    }, 
                    "UsdMtlxMaterialXConfigAPI": {
                        "alias": {
                            "UsdSchemaBase": "MaterialXConfigAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "UsdShadeMaterial"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "MaterialXConfigAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdMtlxParserPlugin": {
                        "bases": [
                            "NdrParserPlugin"
                        ], 
                        "displayName": "MaterialX Node Parser"
                    }
                }
            }, 
            "LibraryPath": "../../../../../Source/ThirdParty/Mac/bin/libusd_usdMtlx.dylib", 
            "Name": "usdMtlx", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
