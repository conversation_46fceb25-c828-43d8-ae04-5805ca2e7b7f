// Copyright Epic Games, Inc. All Rights Reserved.

#include "GASHelperBlueprintLibrary.h"
#include "AbilitySystemComponent.h"
#include "Components/GSCCoreComponent.h"
#include "Abilities/GSCGameplayAbility.h"
#include "Engine/Engine.h"
#include "GameFramework/Actor.h"

FGASActivationResult UGASHelperBlueprintLibrary::QuickActivateAbility(
	AActor* Actor,
	TSubclassOf<UGameplayAbility> AbilityClass,
	bool bAllowRemoteActivation)
{
	if (!Actor)
	{
		return FGASActivationResult::Failure(TEXT("Actor is null"));
	}

	if (!AbilityClass)
	{
		return FGASActivationResult::Failure(TEXT("AbilityClass is null"));
	}

	UGSCCoreComponent* CoreComponent = GetGASCoreComponent(Actor);
	if (!CoreComponent)
	{
		return FGASActivationResult::Failure(TEXT("Actor does not have a GSCCoreComponent"));
	}

	FGASActivationConfig Config;
	Config.bAllowRemoteActivation = bAllowRemoteActivation;
	Config.bLogActivation = true;
	Config.bDetailedErrors = true;

	return InternalActivateAbility(CoreComponent, AbilityClass, Config);
}

FGASActivationResult UGASHelperBlueprintLibrary::QuickActivateAbilityByTags(
	AActor* Actor,
	FGameplayTagContainer AbilityTags,
	bool bAllowRemoteActivation)
{
	if (!Actor)
	{
		return FGASActivationResult::Failure(TEXT("Actor is null"));
	}

	if (AbilityTags.IsEmpty())
	{
		return FGASActivationResult::Failure(TEXT("AbilityTags is empty"));
	}

	UGSCCoreComponent* CoreComponent = GetGASCoreComponent(Actor);
	if (!CoreComponent)
	{
		return FGASActivationResult::Failure(TEXT("Actor does not have a GSCCoreComponent"));
	}

	UGSCGameplayAbility* ActivatedAbility = nullptr;
	bool bSuccess = CoreComponent->ActivateAbilityByTags(AbilityTags, ActivatedAbility, bAllowRemoteActivation);

	if (bSuccess)
	{
		LogActivationAttempt(TEXT("QuickActivateAbilityByTags"), Actor, nullptr, true);
		return FGASActivationResult::Success(ActivatedAbility);
	}
	else
	{
		FString ErrorMessage = FString::Printf(TEXT("Failed to activate ability with tags: %s"), *AbilityTags.ToStringSimple());
		LogActivationAttempt(TEXT("QuickActivateAbilityByTags"), Actor, nullptr, false, ErrorMessage);
		return FGASActivationResult::Failure(ErrorMessage);
	}
}

FGASActivationResult UGASHelperBlueprintLibrary::QuickActivateAbilityByTag(
	AActor* Actor,
	FGameplayTag AbilityTag,
	bool bAllowRemoteActivation)
{
	FGameplayTagContainer TagContainer;
	TagContainer.AddTag(AbilityTag);
	return QuickActivateAbilityByTags(Actor, TagContainer, bAllowRemoteActivation);
}

FGASActivationResult UGASHelperBlueprintLibrary::ActivateAbilityWithConfig(
	AActor* Actor,
	TSubclassOf<UGameplayAbility> AbilityClass,
	const FGASActivationConfig& Config)
{
	if (!Actor)
	{
		return FGASActivationResult::Failure(TEXT("Actor is null"));
	}

	if (!AbilityClass)
	{
		return FGASActivationResult::Failure(TEXT("AbilityClass is null"));
	}

	UGSCCoreComponent* CoreComponent = GetGASCoreComponent(Actor);
	if (!CoreComponent)
	{
		return FGASActivationResult::Failure(TEXT("Actor does not have a GSCCoreComponent"));
	}

	return InternalActivateAbility(CoreComponent, AbilityClass, Config);
}

TArray<FGASActivationResult> UGASHelperBlueprintLibrary::TryActivateAbilities(
	AActor* Actor,
	const TArray<TSubclassOf<UGameplayAbility>>& AbilityClasses,
	bool bStopOnFirstSuccess,
	bool bAllowRemoteActivation)
{
	TArray<FGASActivationResult> Results;

	if (!Actor)
	{
		Results.Add(FGASActivationResult::Failure(TEXT("Actor is null")));
		return Results;
	}

	UGSCCoreComponent* CoreComponent = GetGASCoreComponent(Actor);
	if (!CoreComponent)
	{
		Results.Add(FGASActivationResult::Failure(TEXT("Actor does not have a GSCCoreComponent")));
		return Results;
	}

	FGASActivationConfig Config;
	Config.bAllowRemoteActivation = bAllowRemoteActivation;
	Config.bLogActivation = true;

	for (const TSubclassOf<UGameplayAbility>& AbilityClass : AbilityClasses)
	{
		if (!AbilityClass)
		{
			Results.Add(FGASActivationResult::Failure(TEXT("AbilityClass is null")));
			continue;
		}

		FGASActivationResult Result = InternalActivateAbility(CoreComponent, AbilityClass, Config);
		Results.Add(Result);

		if (bStopOnFirstSuccess && Result.bSuccess)
		{
			break;
		}
	}

	return Results;
}

UGSCCoreComponent* UGASHelperBlueprintLibrary::GetGASCoreComponent(AActor* Actor)
{
	if (!Actor)
	{
		return nullptr;
	}

	return Actor->FindComponentByClass<UGSCCoreComponent>();
}

UAbilitySystemComponent* UGASHelperBlueprintLibrary::GetAbilitySystemComponent(AActor* Actor)
{
	if (!Actor)
	{
		return nullptr;
	}

	// Try to get it through the interface first
	if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(Actor))
	{
		return ASI->GetAbilitySystemComponent();
	}

	// Fallback to component search
	return Actor->FindComponentByClass<UAbilitySystemComponent>();
}

bool UGASHelperBlueprintLibrary::CanActivateAbility(AActor* Actor, TSubclassOf<UGameplayAbility> AbilityClass)
{
	if (!Actor || !AbilityClass)
	{
		return false;
	}

	UAbilitySystemComponent* ASC = GetAbilitySystemComponent(Actor);
	if (!ASC)
	{
		return false;
	}

	// Find the ability spec
	const FGameplayAbilitySpec* AbilitySpec = ASC->FindAbilitySpecFromClass(AbilityClass);
	if (!AbilitySpec)
	{
		return false;
	}

	// Check if the ability can be activated
	return ASC->CanActivateAbility(AbilitySpec->Handle);
}

bool UGASHelperBlueprintLibrary::IsUsingAbility(AActor* Actor, TSubclassOf<UGameplayAbility> AbilityClass)
{
	if (!Actor || !AbilityClass)
	{
		return false;
	}

	UGSCCoreComponent* CoreComponent = GetGASCoreComponent(Actor);
	if (!CoreComponent)
	{
		return false;
	}

	return CoreComponent->IsUsingAbilityByClass(AbilityClass);
}

TArray<UGameplayAbility*> UGASHelperBlueprintLibrary::GetActiveAbilities(AActor* Actor)
{
	TArray<UGameplayAbility*> ActiveAbilities;

	if (!Actor)
	{
		return ActiveAbilities;
	}

	UAbilitySystemComponent* ASC = GetAbilitySystemComponent(Actor);
	if (!ASC)
	{
		return ActiveAbilities;
	}

	// Get all active abilities
	TArray<FGameplayAbilitySpec*> ActivatableAbilities;
	ASC->GetActivatableGameplayAbilitySpecsByAllMatchingTags(FGameplayTagContainer(), ActivatableAbilities);

	for (const FGameplayAbilitySpec* Spec : ActivatableAbilities)
	{
		if (Spec && Spec->IsActive())
		{
			if (UGameplayAbility* Ability = Spec->GetPrimaryInstance())
			{
				ActiveAbilities.Add(Ability);
			}
		}
	}

	return ActiveAbilities;
}

int32 UGASHelperBlueprintLibrary::CancelAbilitiesWithTags(AActor* Actor, FGameplayTagContainer CancelTags)
{
	if (!Actor || CancelTags.IsEmpty())
	{
		return 0;
	}

	UAbilitySystemComponent* ASC = GetAbilitySystemComponent(Actor);
	if (!ASC)
	{
		return 0;
	}

	int32 CancelledCount = 0;
	ASC->CancelAbilities(&CancelTags);

	// Note: The actual count of cancelled abilities is not easily retrievable from the ASC
	// This is a limitation of the current GAS API
	return CancelledCount;
}

int32 UGASHelperBlueprintLibrary::CancelAllAbilities(AActor* Actor)
{
	if (!Actor)
	{
		return 0;
	}

	UAbilitySystemComponent* ASC = GetAbilitySystemComponent(Actor);
	if (!ASC)
	{
		return 0;
	}

	ASC->CancelAllAbilities();
	return 1; // Return 1 to indicate the operation was performed
}

FGASActivationResult UGASHelperBlueprintLibrary::InternalActivateAbility(
	UGSCCoreComponent* CoreComponent,
	TSubclassOf<UGameplayAbility> AbilityClass,
	const FGASActivationConfig& Config)
{
	if (!CoreComponent || !AbilityClass)
	{
		return FGASActivationResult::Failure(TEXT("Invalid CoreComponent or AbilityClass"));
	}

	UGSCGameplayAbility* ActivatedAbility = nullptr;
	bool bSuccess = CoreComponent->ActivateAbilityByClass(AbilityClass, ActivatedAbility, Config.bAllowRemoteActivation);

	if (bSuccess)
	{
		if (Config.bLogActivation)
		{
			LogActivationAttempt(TEXT("InternalActivateAbility"), CoreComponent->GetOwner(), AbilityClass, true);
		}

		// Try to get the ability handle
		FGameplayAbilitySpecHandle Handle;
		if (UAbilitySystemComponent* ASC = CoreComponent->GetAbilitySystemComponent())
		{
			if (const FGameplayAbilitySpec* Spec = ASC->FindAbilitySpecFromClass(AbilityClass))
			{
				Handle = Spec->Handle;
			}
		}

		return FGASActivationResult::Success(ActivatedAbility, Handle);
	}
	else
	{
		FString ErrorMessage = FString::Printf(TEXT("Failed to activate ability: %s"), 
			AbilityClass ? *AbilityClass->GetName() : TEXT("Unknown"));

		if (Config.bLogActivation)
		{
			LogActivationAttempt(TEXT("InternalActivateAbility"), CoreComponent->GetOwner(), AbilityClass, false, ErrorMessage);
		}

		return FGASActivationResult::Failure(ErrorMessage);
	}
}

void UGASHelperBlueprintLibrary::LogActivationAttempt(
	const FString& FunctionName,
	AActor* Actor,
	TSubclassOf<UGameplayAbility> AbilityClass,
	bool bSuccess,
	const FString& ErrorMessage)
{
	FString ActorName = Actor ? Actor->GetName() : TEXT("Unknown");
	FString AbilityName = AbilityClass ? AbilityClass->GetName() : TEXT("Unknown");

	if (bSuccess)
	{
		UE_LOG(LogTemp, Log, TEXT("[GASHelper::%s] Successfully activated ability '%s' on actor '%s'"), 
			*FunctionName, *AbilityName, *ActorName);
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("[GASHelper::%s] Failed to activate ability '%s' on actor '%s': %s"), 
			*FunctionName, *AbilityName, *ActorName, *ErrorMessage);
	}
}
