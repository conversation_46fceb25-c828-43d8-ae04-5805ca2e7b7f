// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GameplayAbilitySpec.h"
#include "GameplayTagContainer.h"
#include "GASHelperBlueprintLibrary.h"
#include "GASAbilityActivationHelperComponent.generated.h"

class UGameplayAbility;
class UAbilitySystemComponent;
class UGSCCoreComponent;
class UGSCGameplayAbility;

/**
 * Delegate for ability activation events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnAbilityActivationAttempt, 
	TSubclassOf<UGameplayAbility>, AbilityClass, 
	bool, bSuccess, 
	const FString&, ErrorMessage);

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAbilityActivated, 
	TSubclassOf<UGameplayAbility>, AbilityClass, 
	UGSCGameplayAbility*, ActivatedAbility);

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAbilityActivationFailed, 
	TSubclassOf<UGameplayAbility>, AbilityClass, 
	const FString&, ErrorMessage);

/**
 * Configuration for batch ability operations
 */
USTRUCT(BlueprintType)
struct PROJECTGUNK_API FGASBatchActivationConfig
{
	GENERATED_BODY()

	/** Abilities to try activating */
	UPROPERTY(BlueprintReadWrite, Category = "Batch Activation")
	TArray<TSubclassOf<UGameplayAbility>> AbilityClasses;

	/** Whether to stop after the first successful activation */
	UPROPERTY(BlueprintReadWrite, Category = "Batch Activation")
	bool bStopOnFirstSuccess = true;

	/** Whether to allow remote activation */
	UPROPERTY(BlueprintReadWrite, Category = "Batch Activation")
	bool bAllowRemoteActivation = true;

	/** Delay between activation attempts (in seconds) */
	UPROPERTY(BlueprintReadWrite, Category = "Batch Activation")
	float DelayBetweenActivations = 0.0f;

	/** Whether to log each activation attempt */
	UPROPERTY(BlueprintReadWrite, Category = "Batch Activation")
	bool bLogActivations = true;
};

/**
 * Advanced helper component for GAS ability activation with fluent API and event handling
 * Provides more sophisticated activation patterns, caching, and event-driven functionality
 */
UCLASS(ClassGroup=(GASHelper), meta=(BlueprintSpawnableComponent))
class PROJECTGUNK_API UGASAbilityActivationHelperComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UGASAbilityActivationHelperComponent();

protected:
	virtual void BeginPlay() override;

public:
	// ========================================
	// Events
	// ========================================

	/** Called whenever an ability activation is attempted */
	UPROPERTY(BlueprintAssignable, Category = "GAS Helper|Events")
	FOnAbilityActivationAttempt OnAbilityActivationAttempt;

	/** Called when an ability is successfully activated */
	UPROPERTY(BlueprintAssignable, Category = "GAS Helper|Events")
	FOnAbilityActivated OnAbilityActivated;

	/** Called when an ability activation fails */
	UPROPERTY(BlueprintAssignable, Category = "GAS Helper|Events")
	FOnAbilityActivationFailed OnAbilityActivationFailed;

	// ========================================
	// Fluent Activation API
	// ========================================

	/**
	 * Start a fluent activation chain
	 * @param AbilityClass The ability class to activate
	 * @return This component for method chaining
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Fluent API")
	UGASAbilityActivationHelperComponent* WithAbility(TSubclassOf<UGameplayAbility> AbilityClass);

	/**
	 * Set whether to allow remote activation in the fluent chain
	 * @param bAllow Whether to allow remote activation
	 * @return This component for method chaining
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Fluent API")
	UGASAbilityActivationHelperComponent* AllowRemoteActivation(bool bAllow = true);

	/**
	 * Set whether to log activation attempts in the fluent chain
	 * @param bLog Whether to log activation attempts
	 * @return This component for method chaining
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Fluent API")
	UGASAbilityActivationHelperComponent* WithLogging(bool bLog = true);

	/**
	 * Add a delay before activation in the fluent chain
	 * @param DelaySeconds Delay in seconds
	 * @return This component for method chaining
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Fluent API")
	UGASAbilityActivationHelperComponent* WithDelay(float DelaySeconds);

	/**
	 * Execute the fluent activation chain
	 * @return Activation result
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Fluent API")
	FGASActivationResult Execute();

	/**
	 * Execute the fluent activation chain asynchronously
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Fluent API")
	void ExecuteAsync();

	// ========================================
	// Batch Operations
	// ========================================

	/**
	 * Activate multiple abilities with configuration
	 * @param Config Configuration for batch activation
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Batch Operations")
	void ActivateAbilitiesBatch(const FGASBatchActivationConfig& Config);

	/**
	 * Try to activate abilities by priority (first in array has highest priority)
	 * @param AbilityClasses Abilities in priority order
	 * @param bAllowRemoteActivation Whether to allow remote activation
	 * @return Result of the first successful activation
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Batch Operations")
	FGASActivationResult ActivateAbilityByPriority(
		const TArray<TSubclassOf<UGameplayAbility>>& AbilityClasses,
		bool bAllowRemoteActivation = true
	);

	// ========================================
	// Smart Activation
	// ========================================

	/**
	 * Activate an ability only if it's not currently active
	 * @param AbilityClass The ability class to activate
	 * @param bAllowRemoteActivation Whether to allow remote activation
	 * @return Activation result
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Smart Activation")
	FGASActivationResult ActivateIfNotActive(
		TSubclassOf<UGameplayAbility> AbilityClass,
		bool bAllowRemoteActivation = true
	);

	/**
	 * Activate an ability and cancel any conflicting abilities
	 * @param AbilityClass The ability class to activate
	 * @param ConflictingTags Tags of abilities to cancel before activation
	 * @param bAllowRemoteActivation Whether to allow remote activation
	 * @return Activation result
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Smart Activation")
	FGASActivationResult ActivateWithCancelConflicting(
		TSubclassOf<UGameplayAbility> AbilityClass,
		FGameplayTagContainer ConflictingTags,
		bool bAllowRemoteActivation = true
	);

	/**
	 * Try to activate an ability, with fallback abilities if the first fails
	 * @param PrimaryAbility The primary ability to try first
	 * @param FallbackAbilities Fallback abilities to try if primary fails
	 * @param bAllowRemoteActivation Whether to allow remote activation
	 * @return Result of the first successful activation
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Smart Activation")
	FGASActivationResult ActivateWithFallback(
		TSubclassOf<UGameplayAbility> PrimaryAbility,
		const TArray<TSubclassOf<UGameplayAbility>>& FallbackAbilities,
		bool bAllowRemoteActivation = true
	);

	// ========================================
	// Caching and Performance
	// ========================================

	/**
	 * Cache frequently used ability specs for faster activation
	 * @param AbilityClasses Abilities to cache
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Performance")
	void CacheAbilitySpecs(const TArray<TSubclassOf<UGameplayAbility>>& AbilityClasses);

	/**
	 * Clear the ability spec cache
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Performance")
	void ClearAbilitySpecCache();

	/**
	 * Get a cached ability spec handle
	 * @param AbilityClass The ability class to get the handle for
	 * @return The cached handle, or invalid handle if not cached
	 */
	UFUNCTION(BlueprintPure, Category = "GAS Helper|Performance")
	FGameplayAbilitySpecHandle GetCachedAbilityHandle(TSubclassOf<UGameplayAbility> AbilityClass);

	// ========================================
	// Utility Functions
	// ========================================

	/**
	 * Get the owner's GSC Core Component
	 * @return The GSC Core Component if found
	 */
	UFUNCTION(BlueprintPure, Category = "GAS Helper|Utilities")
	UGSCCoreComponent* GetOwnerGSCCoreComponent() const;

	/**
	 * Get the owner's Ability System Component
	 * @return The Ability System Component if found
	 */
	UFUNCTION(BlueprintPure, Category = "GAS Helper|Utilities")
	UAbilitySystemComponent* GetOwnerAbilitySystemComponent() const;

	/**
	 * Check if the owner can activate a specific ability
	 * @param AbilityClass The ability class to check
	 * @return True if the ability can be activated
	 */
	UFUNCTION(BlueprintPure, Category = "GAS Helper|Utilities")
	bool CanOwnerActivateAbility(TSubclassOf<UGameplayAbility> AbilityClass) const;

protected:
	// ========================================
	// Internal State for Fluent API
	// ========================================

	/** Current ability class for fluent API */
	UPROPERTY()
	TSubclassOf<UGameplayAbility> FluentAbilityClass;

	/** Current configuration for fluent API */
	UPROPERTY()
	FGASActivationConfig FluentConfig;

	/** Delay for fluent API */
	float FluentDelay = 0.0f;

	// ========================================
	// Caching
	// ========================================

	/** Cached ability spec handles for performance */
	UPROPERTY()
	TMap<TSubclassOf<UGameplayAbility>, FGameplayAbilitySpecHandle> CachedAbilityHandles;

	/** Cached GSC Core Component reference */
	UPROPERTY()
	TObjectPtr<UGSCCoreComponent> CachedGSCCoreComponent;

	/** Cached Ability System Component reference */
	UPROPERTY()
	TObjectPtr<UAbilitySystemComponent> CachedAbilitySystemComponent;

	// ========================================
	// Internal Helper Functions
	// ========================================

	void RefreshComponentCache();
	void ExecuteFluentActivation();
	FGASActivationResult InternalActivateAbility(TSubclassOf<UGameplayAbility> AbilityClass, const FGASActivationConfig& Config);
	void BroadcastActivationEvents(TSubclassOf<UGameplayAbility> AbilityClass, const FGASActivationResult& Result);

	// Timer handle for delayed activation
	FTimerHandle DelayedActivationTimerHandle;
};
