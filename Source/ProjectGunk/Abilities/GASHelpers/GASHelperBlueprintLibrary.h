// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "GameplayAbilitySpec.h"
#include "GameplayTagContainer.h"
#include "AbilitySystemComponent.h"
#include "Abilities/GSCGameplayAbility.h"
#include "Components/GSCCoreComponent.h"
#include "GASHelperBlueprintLibrary.generated.h"

class UGameplayAbility;
class UAbilitySystemComponent;
class UGSCCoreComponent;

/**
 * Result structure for ability activation attempts
 */
USTRUCT(BlueprintType)
struct PROJECTGUNK_API FGASActivationResult
{
	GENERATED_BODY()

	/** Whether the activation was successful */
	UPROPERTY(BlueprintReadOnly, Category = "GAS Helper")
	bool bSuccess = false;

	/** The activated ability instance (if successful and available) */
	UPROPERTY(BlueprintReadOnly, Category = "GAS Helper")
	TObjectPtr<UGSCGameplayAbility> ActivatedAbility = nullptr;

	/** Error message if activation failed */
	UPROPERTY(BlueprintReadOnly, Category = "GAS Helper")
	FString ErrorMessage;

	/** The ability spec handle (if successful) */
	UPROPERTY(BlueprintReadOnly, Category = "GAS Helper")
	FGameplayAbilitySpecHandle AbilityHandle;

	FGASActivationResult() = default;

	FGASActivationResult(bool bInSuccess, UGSCGameplayAbility* InAbility = nullptr, const FString& InErrorMessage = TEXT(""))
		: bSuccess(bInSuccess), ActivatedAbility(InAbility), ErrorMessage(InErrorMessage) {}

	static FGASActivationResult Success(UGSCGameplayAbility* InAbility = nullptr, FGameplayAbilitySpecHandle InHandle = FGameplayAbilitySpecHandle())
	{
		FGASActivationResult Result(true, InAbility);
		Result.AbilityHandle = InHandle;
		return Result;
	}

	static FGASActivationResult Failure(const FString& InErrorMessage)
	{
		return FGASActivationResult(false, nullptr, InErrorMessage);
	}
};

/**
 * Configuration structure for ability activation
 */
USTRUCT(BlueprintType)
struct PROJECTGUNK_API FGASActivationConfig
{
	GENERATED_BODY()

	/** Whether to allow remote activation */
	UPROPERTY(BlueprintReadWrite, Category = "GAS Helper")
	bool bAllowRemoteActivation = true;

	/** Whether to log activation attempts */
	UPROPERTY(BlueprintReadWrite, Category = "GAS Helper")
	bool bLogActivation = true;

	/** Whether to automatically handle authority checks */
	UPROPERTY(BlueprintReadWrite, Category = "GAS Helper")
	bool bAutoHandleAuthority = true;

	/** Whether to return detailed error messages */
	UPROPERTY(BlueprintReadWrite, Category = "GAS Helper")
	bool bDetailedErrors = true;

	/** Custom log category for activation messages */
	UPROPERTY(BlueprintReadWrite, Category = "GAS Helper")
	FString LogCategory = TEXT("GASHelper");
};

/**
 * Blueprint Function Library providing simplified GAS ability activation helpers
 * Reduces boilerplate code and provides consistent error handling and logging
 */
UCLASS()
class PROJECTGUNK_API UGASHelperBlueprintLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	// ========================================
	// Quick Activation Functions
	// ========================================

	/**
	 * Quick ability activation by class with minimal setup
	 * @param Actor The actor to activate the ability on
	 * @param AbilityClass The ability class to activate
	 * @param bAllowRemoteActivation Whether to allow remote activation
	 * @return Activation result with success status and activated ability
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Quick Activation", meta = (CallInEditor = "true"))
	static FGASActivationResult QuickActivateAbility(
		AActor* Actor,
		TSubclassOf<UGameplayAbility> AbilityClass,
		bool bAllowRemoteActivation = true
	);

	/**
	 * Quick ability activation by gameplay tags
	 * @param Actor The actor to activate the ability on
	 * @param AbilityTags Tags to match for ability activation
	 * @param bAllowRemoteActivation Whether to allow remote activation
	 * @return Activation result with success status and activated ability
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Quick Activation", meta = (CallInEditor = "true"))
	static FGASActivationResult QuickActivateAbilityByTags(
		AActor* Actor,
		FGameplayTagContainer AbilityTags,
		bool bAllowRemoteActivation = true
	);

	/**
	 * Quick ability activation by single tag
	 * @param Actor The actor to activate the ability on
	 * @param AbilityTag Single tag to match for ability activation
	 * @param bAllowRemoteActivation Whether to allow remote activation
	 * @return Activation result with success status and activated ability
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Quick Activation", meta = (CallInEditor = "true"))
	static FGASActivationResult QuickActivateAbilityByTag(
		AActor* Actor,
		FGameplayTag AbilityTag,
		bool bAllowRemoteActivation = true
	);

	// ========================================
	// Advanced Activation Functions
	// ========================================

	/**
	 * Advanced ability activation with full configuration
	 * @param Actor The actor to activate the ability on
	 * @param AbilityClass The ability class to activate
	 * @param Config Configuration for the activation
	 * @return Activation result with detailed information
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Advanced Activation")
	static FGASActivationResult ActivateAbilityWithConfig(
		AActor* Actor,
		TSubclassOf<UGameplayAbility> AbilityClass,
		const FGASActivationConfig& Config
	);

	/**
	 * Try to activate multiple abilities in sequence
	 * @param Actor The actor to activate abilities on
	 * @param AbilityClasses Array of ability classes to try activating
	 * @param bStopOnFirstSuccess Whether to stop after the first successful activation
	 * @param bAllowRemoteActivation Whether to allow remote activation
	 * @return Array of activation results for each ability
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Advanced Activation")
	static TArray<FGASActivationResult> TryActivateAbilities(
		AActor* Actor,
		const TArray<TSubclassOf<UGameplayAbility>>& AbilityClasses,
		bool bStopOnFirstSuccess = true,
		bool bAllowRemoteActivation = true
	);

	// ========================================
	// Utility Functions
	// ========================================

	/**
	 * Get the GAS Core Component from an actor
	 * @param Actor The actor to get the component from
	 * @return The GSC Core Component if found, nullptr otherwise
	 */
	UFUNCTION(BlueprintPure, Category = "GAS Helper|Utilities")
	static UGSCCoreComponent* GetGASCoreComponent(AActor* Actor);

	/**
	 * Get the Ability System Component from an actor
	 * @param Actor The actor to get the component from
	 * @return The Ability System Component if found, nullptr otherwise
	 */
	UFUNCTION(BlueprintPure, Category = "GAS Helper|Utilities")
	static UAbilitySystemComponent* GetAbilitySystemComponent(AActor* Actor);

	/**
	 * Check if an actor can activate a specific ability
	 * @param Actor The actor to check
	 * @param AbilityClass The ability class to check
	 * @return True if the ability can be activated
	 */
	UFUNCTION(BlueprintPure, Category = "GAS Helper|Utilities")
	static bool CanActivateAbility(AActor* Actor, TSubclassOf<UGameplayAbility> AbilityClass);

	/**
	 * Check if an actor is currently using a specific ability
	 * @param Actor The actor to check
	 * @param AbilityClass The ability class to check
	 * @return True if the ability is currently active
	 */
	UFUNCTION(BlueprintPure, Category = "GAS Helper|Utilities")
	static bool IsUsingAbility(AActor* Actor, TSubclassOf<UGameplayAbility> AbilityClass);

	/**
	 * Get all currently active abilities on an actor
	 * @param Actor The actor to check
	 * @return Array of currently active abilities
	 */
	UFUNCTION(BlueprintPure, Category = "GAS Helper|Utilities")
	static TArray<UGameplayAbility*> GetActiveAbilities(AActor* Actor);

	// ========================================
	// Batch Operations
	// ========================================

	/**
	 * Cancel all abilities with specific tags
	 * @param Actor The actor to cancel abilities on
	 * @param CancelTags Tags of abilities to cancel
	 * @return Number of abilities cancelled
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Batch Operations")
	static int32 CancelAbilitiesWithTags(AActor* Actor, FGameplayTagContainer CancelTags);

	/**
	 * Cancel all active abilities on an actor
	 * @param Actor The actor to cancel abilities on
	 * @return Number of abilities cancelled
	 */
	UFUNCTION(BlueprintCallable, Category = "GAS Helper|Batch Operations")
	static int32 CancelAllAbilities(AActor* Actor);

private:
	// Helper functions for internal use
	static FGASActivationResult InternalActivateAbility(
		UGSCCoreComponent* CoreComponent,
		TSubclassOf<UGameplayAbility> AbilityClass,
		const FGASActivationConfig& Config
	);

	static void LogActivationAttempt(
		const FString& FunctionName,
		AActor* Actor,
		TSubclassOf<UGameplayAbility> AbilityClass,
		bool bSuccess,
		const FString& ErrorMessage = TEXT("")
	);
};
