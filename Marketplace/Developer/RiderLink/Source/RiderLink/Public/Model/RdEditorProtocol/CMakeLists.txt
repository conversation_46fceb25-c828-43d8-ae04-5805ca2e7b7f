cmake_minimum_required(VERSION 3.7)
add_library(RdEditorRoot STATIC 
instantiations_RdEditorRoot.h
instantiations_RdEditorRoot.cpp
LiveCodingModel/LiveCodingModel.Pregenerated.cpp
LiveCodingModel/LiveCodingModel.Pregenerated.h
RdEditorModel/RdEditorModel.Pregenerated.cpp
RdEditorModel/RdEditorModel.Pregenerated.h
RdEditorRoot/RdEditorRoot.Pregenerated.cpp
RdEditorRoot/RdEditorRoot.Pregenerated.h)
target_include_directories(RdEditorRoot PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})
target_link_libraries(RdEditorRoot PUBLIC rd_framework_cpp)
