<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Alembic</Name>
  <Location>Engine/Source/ThirdParty/Alembic</Location>
  <Function>Alembic is an interchange framework for transferring static and animated geometry between software packages. It is a core component of the software specification for the VFX reference platform.</Function>
  <Eula>https://github.com/alembic/alembic/blob/1.8.7/LICENSE.txt</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
