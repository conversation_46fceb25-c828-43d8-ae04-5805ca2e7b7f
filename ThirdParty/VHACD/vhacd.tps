<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>V-HACD</Name>
  <Location>/Engine/Source/ThirdParty/VHACD/</Location>
  <Date>2016-06-16T14:04:45.8313745-04:00</Date>
  <Function>Decomposes tri mesh into convex hull approximation for collision</Function>
  <Justification>Currently we use HACD which doesn't work that well, this is an improvement.</Justification>
  <Eula>http://opensource.org/licenses/BSD-3-Clause</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/V-hacd_license.txt</LicenseFolder>
</TpsData>