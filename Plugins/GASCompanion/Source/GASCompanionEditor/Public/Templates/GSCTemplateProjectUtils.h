// Copyright 2021 <PERSON><PERSON>. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameProjectUtils.h"

class GASCOMPANIONEDITOR_API FGSCTemplateProjectUtils
{

public:

	/** Adds new source code to the project. When returning Succeeded or FailedToHotReload, OutSyncFileAndLineNumber will be the the preferred target file to sync in the users code editing IDE, formatted for use with GenericApplication::GotoLineInSource */
	static GameProjectUtils::EAddCodeToProjectResult AddCodeToProject(const FString& NewClassName, const FString& NewClassPath, const FModuleContextInfo& ModuleInfo, const FNewClassInfo ParentClassInfo, const TSet<FString>& DisallowedHeaderNames, FString& OutHeaderFilePath, FString& OutCppFilePath, FText& OutFailReason);

	/**
	* Creates the basic source code for a new project. On failure, OutFailReason will be populated.
	*
	* A new Build.cs file will be generated for the game module.
	*
	* We need to update Build.cs file to include proper PrivateDependencyModuleNames.
	*
	* It is generated by AddCodeToProject() for project that don't have code yet, but private dependency module needs to include:
	*
	* - GASCompanion
	* - GameplayAbilities
	* - GameplayTasks
	* - GameplayTags
	*/
	static bool GenerateBasicSourceCode(const FString& NewProjectSourcePath, const FString& NewProjectName, const FString& NewProjectRoot, TArray<FString>& OutGeneratedStartupModuleNames, TArray<FString>& OutCreatedFiles, FText& OutFailReason);

	/**
	* Prepare Class Template file for generation, will replace special %% placeholders not handled by GameProjectUtils,
	* namely for Attribute properties generation.
	*/
	static bool PrepareTemplate(FNewClassInfo ParentClassInfo, FText& OutFailReason);

	/**
	* Restore Class Template file to their original content before being edited by PrepareTemplate()
	*/
	static bool ResetTemplate(FText& OutFailReason);

	/**
	* Gets required additional dependencies for fresh project for given class info.
	*
	* @param ClassInfo Given class info.
	*
	* @returns Array of required dependencies.
	*/
	static TArray<FString> GetRequiredAdditionalDependencies(const FNewClassInfo& ClassInfo);

	/** Generates a header file for a UObject class. OutSyncLocation is a string representing the preferred cursor sync location for this file after creation. */
	static bool GenerateClassHeaderFile(const FString& NewHeaderFileName, const FString UnPrefixedClassName, const FNewClassInfo ParentClassInfo, const TArray<FString>& ClassSpecifierList, const FString& ClassProperties, const FString& ClassFunctionDeclarations, FString& OutSyncLocation, const FModuleContextInfo& ModuleInfo, bool bDeclareConstructor, FText& OutFailReason);

	/** Generates a cpp file for a UObject class */
	static bool GenerateClassCPPFile(const FString& NewCPPFileName, const FString UnPrefixedClassName, const FNewClassInfo ParentClassInfo, const TArray<FString>& AdditionalIncludes, const TArray<FString>& PropertyOverrides, const FString& AdditionalMemberDefinitions, FString& OutSyncLocation, const FModuleContextInfo& ModuleInfo, FText& OutFailReason);

	/** Returns the copyright line used at the top of all files */
	static FString MakeCopyrightLine();

	/** Finds the cursor sync location in the source file and reports it back as a string */
	static void HarvestCursorSyncLocation( FString& FinalOutput, FString& OutSyncLocation );

	/** Returns the include header path for a given fully specified, normalized file path */
	static FString GetIncludePathForFile(const FString& InFullFilePath, const FString& ModuleRootPath);

	/**
	* Replace a wildcard with another string
	*
	* @param Input				The input string
	* @param From				The wildcard to be replaced
	* @param To					The text the wildcard should be replaced with
	* @param bLeadingTab		If the line where the wildcard is located starts with a tab
	* @param bTrailingNewLine	If the line where the wildcard is located ends with a new line

	* @return the input string after replacement of the wildcard
	*/
	static FString ReplaceWildcard(const FString& Input, const FString& From, const FString& To, bool bLeadingTab = false, bool bTrailingNewLine = false);

	/**
	 * Updates the projects and modifies FProjectDescriptor accordingly to given modifier.
	 *
	 * @param Modifier	Callback delegate that will modify the project descriptor accordingly.
	 */
	static void UpdateProject(const FProjectDescriptorModifier& Modifier);

	/**
	 * Updates the loaded game project file to the current version and modifies FProjectDescriptor accordingly to given modifier.
	 *
	 * @param ProjectFilename		The name of the project (used to checkout from source control)
	 * @param EngineIdentifier		The identifier for the engine to open the project with
	 * @param Modifier				Callback delegate that will modify the project descriptor accordingly.
	 * @param OutFailReason			Out, if unsuccessful this is the reason why

	 * @return true, if successful
	 */
	static bool UpdateGameProjectFile_Impl(const FString& ProjectFilename, const FString& EngineIdentifier, const FProjectDescriptorModifier* Modifier, FText& OutFailReason);

	/** Checks out the current project file (or prompts to make writable) */
	static void TryMakeProjectFileWriteable(const FString& ProjectFile);

	/** Checks the specified game project file out from source control */
	static bool CheckoutGameProjectFile(const FString& ProjectFilename, FText& OutFailReason);

	/**
	* Updates startup module names in project descriptor.
	*
	* @param Descriptor Descriptor to update.
	* @param StartupModuleNames Modules to fill.
	*
	* @returns True if descriptor has been modified. False otherwise.
	*/
	static bool UpdateStartupModuleNames(FProjectDescriptor& Descriptor, const TArray<FString>* StartupModuleNames);

	/**
	 * Updates additional dependencies in project descriptor.
	 *
	 * @param Descriptor Descriptor to update.
	 * @param RequiredDependencies Required dependencies.
	 * @param ModuleName Module name for which those dependencies are required.
	 *
	 * @returns True if descriptor has been modified. False otherwise.
	 */
	static bool UpdateRequiredAdditionalDependencies(FProjectDescriptor& Descriptor, TArray<FString>& RequiredDependencies, const FString& ModuleName);

	/** Get the C++ prefix used for this class type */
	static FString GetClassPrefixCPP(FNewClassInfo ClassInfo);

	/** Get the C++ class name; this may or may not be prefixed, but will always produce a valid C++ name via GetClassPrefix() + GetClassName() */
	static FString GetClassNameCPP(FNewClassInfo ClassInfo);

	/** Some classes may apply a particular suffix; this function returns the class name with those suffixes removed */
	static FString GetCleanClassName(FNewClassInfo ClassInfo, const FString& ClassName);

	/** Some classes may apply a particular suffix; this function returns the class name that will ultimately be used should that happen */
	static FString GetFinalClassName(FNewClassInfo ClassInfo, const FString& ClassName);

	/** Get the path needed to include this class into another file */
	static bool GetIncludePath(FNewClassInfo ClassInfo, FString& OutIncludePath);

	/** Given a class name, generate the header file (.h) that should be used for this class */
	static FString GetHeaderFilename(FNewClassInfo ClassInfo, const FString& ClassName);

	/** Given a class name, generate the source file (.cpp) that should be used for this class */
	static FString GetSourceFilename(FNewClassInfo, const FString& ClassName);

	/** Get the generation template filename to used based on the current class type */
	static FString GetHeaderTemplateFilename(FNewClassInfo ClassInfo);

	/** Get the generation template filename to used based on the current class type */
	static FString GetSourceTemplateFilename(FNewClassInfo ClassInfo);

	/** Generates a Target.cs file for a game module */
	static bool GenerateGameModuleTargetFile(const FString& NewTargetFileName, const FString& ModuleName, const TArray<FString>& ExtraModuleNames, FText& OutFailReason);

	/** Generates a Target.cs file for a Editor module */
	static bool GenerateEditorModuleTargetFile(const FString& NewTargetFileName, const FString& ModuleName, const TArray<FString>& ExtraModuleNames, FText& OutFailReason);

	/** Generates a main game module header file */
	static bool GenerateGameModuleHeaderFile(const FString& NewGameModuleHeaderFileName, const TArray<FString>& PublicHeaderIncludes, FText& OutFailReason);

	/** Generates a main game module cpp file */
	static bool GenerateGameModuleCPPFile(const FString& NewGameModuleCPPFileName, const FString& ModuleName, const FString& GameName, FText& OutFailReason);
private:

	static TWeakPtr<SNotificationItem> UpdateGameProjectNotification;
	constexpr static const TCHAR IncludePathFormatString[] = TEXT("#include \"%s\"");
};
