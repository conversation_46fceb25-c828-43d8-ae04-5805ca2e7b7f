// Copyright 2022-2024 <PERSON><PERSON>. All Rights Reserved.

#include "UI/Widgets/SGSCAttributeListReferenceViewer.h"

#include "AbilitySystemComponent.h"
#include "Editor.h"
#include "AssetRegistry/AssetIdentifier.h"
#include "Core/Logging/GASCompanionEditorLog.h"
#include "UObject/UObjectIterator.h"
#include "Widgets/Input/SComboBox.h"
#include "Widgets/Input/SSearchBox.h"

#define LOCTEXT_NAMESPACE "GBAAttributeListReferenceViewer"

namespace UE::GASCompanionEditor::Private
{
	/** Checks whether the attribute set class has to be considered to generate dropdown (filters out SKEL / REINST BP Class Generated By) */
	static bool IsValidAttributeClass(const UClass* InClass)
	{
		if (!InClass)
		{
			return false;
		}

		if (!InClass->IsChildOf(UAttributeSet::StaticClass()))
		{
			return false;
		}

		return true;
	}
	
	/**
	 * Returns whether given property "CPPType" is a valid type to consider for attributes
	 *
	 * Restricting to only FGameplayAttributeData and child of it (that we know of)
	 */
	static bool IsValidCPPType(const FString& InCPPType)
	{
		if (InCPPType == TEXT("FGameplayAttributeData"))
		{
			return true;
		}

		// TODO: Check using FGameplayAttribute::IsGameplayAttributeDataProperty

		return false;
	}
	
	static bool IsValidCPPType(const FProperty* InProperty)
	{
		return FGameplayAttribute::IsGameplayAttributeDataProperty(InProperty);
	}
	
	/** Strips out any trailing "_C" from Blueprint class names */
	static FString GetAttributeClassName(const UClass* Class)
	{
		if (!Class)
		{
			return TEXT("None");
		}

#if WITH_EDITORONLY_DATA
		if (!Class->ClassGeneratedBy)
		{
			return Class->GetName();
		}
#endif
		
		FString ClassName = Class->GetName();
		if (ClassName.IsEmpty())
		{
			return ClassName;
		}

		ClassName.RemoveFromEnd(TEXT("_C"));
		return ClassName;
	}
}

/** The item used for visualizing the attribute in the list. */
class SGSCAttributeListReferenceViewerItem : public SComboRow<TSharedPtr<FGSCAttributeListReferenceViewerNode>>
{
public:
	SLATE_BEGIN_ARGS(SGSCAttributeListReferenceViewerItem)
			: _TextColor(FLinearColor(1.0f, 1.0f, 1.0f, 1.0f))
		{
		}

		/** The text this item should highlight, if any. */
		SLATE_ARGUMENT(FText, HighlightText)
		/** The color text this item will use. */
		SLATE_ARGUMENT(FSlateColor, TextColor)
		/** The node this item is associated with. */
		SLATE_ARGUMENT(TSharedPtr<FGSCAttributeListReferenceViewerNode>, AssociatedNode)

	SLATE_END_ARGS()

	void Construct(const FArguments& InArgs, const TSharedRef<STableViewBase>& InOwnerTableView)
	{
		AssociatedNode = InArgs._AssociatedNode;

		ChildSlot
		[
			SNew(SHorizontalBox)

			+SHorizontalBox::Slot()
			.FillWidth(1.0f)
			.Padding(0.0f, 3.0f, 6.0f, 3.0f)
			.VAlign(VAlign_Center)
			[
				SNew(STextBlock)
				.Text(FText::FromString(*AssociatedNode->AttributeName.Get()))
				.HighlightText(InArgs._HighlightText)
				.ColorAndOpacity(this, &SGSCAttributeListReferenceViewerItem::GetTextColor)
				.IsEnabled(true)
			]
		];

		TextColor = InArgs._TextColor;

		ConstructInternal(
			STableRow::FArguments()
			.ShowSelection(true),
			InOwnerTableView
		);
	}

	/** Returns the text color for the item based on if it is selected or not. */
	FSlateColor GetTextColor() const
	{
		const TSharedPtr<ITypedTableView<TSharedPtr<FGSCAttributeListReferenceViewerNode>>> OwnerWidget = OwnerTablePtr.Pin();
		const TSharedPtr<FGSCAttributeListReferenceViewerNode>* MyItem = OwnerWidget->Private_ItemFromWidget(this);
		const bool bIsSelected = OwnerWidget->Private_IsItemSelected(*MyItem);

		if (bIsSelected)
		{
			return FSlateColor::UseForeground();
		}

		return TextColor;
	}

private:
	/** The text color for this item. */
	FSlateColor TextColor;

	/** The Attribute Viewer Node this item is associated with. */
	TSharedPtr<FGSCAttributeListReferenceViewerNode> AssociatedNode;
};

void SGSCAttributeListReferenceViewer::Construct(const FArguments& InArgs)
{
	struct FLocal
	{
		static void AttributeToStringArray(const FProperty& Property, OUT TArray<FString>& StringArray)
		{
			const UClass* Class = Property.GetOwnerClass();
			if (Class->IsChildOf(UAttributeSet::StaticClass()) ||
				(Class->IsChildOf(UAbilitySystemComponent::StaticClass()) && !Class->ClassGeneratedBy))
			{
				StringArray.Add(FString::Printf(TEXT("%s.%s"), *Class->GetName(), *Property.GetName()));
			}
		}
	};
	
	// Setup text filtering
	AttributeTextFilter = MakeShared<FAttributeTextFilter>(FAttributeTextFilter::FItemToStringArray::CreateStatic(&FLocal::AttributeToStringArray));
	
	UpdatePropertyOptions();
	
	TWeakPtr<SGSCAttributeListReferenceViewer> WeakSelf = StaticCastWeakPtr<SGSCAttributeListReferenceViewer>(AsWeak());

	const TSharedRef<SWidget> Picker = 
		SNew(SBorder)
		.Padding(InArgs._Padding)
		.BorderImage(FStyleDefaults::GetNoBrush())
		[
			SNew(SVerticalBox)

			+SVerticalBox::Slot()
			.AutoHeight()
			.VAlign(VAlign_Top)
			[
				SNew(SHorizontalBox)

				// Search
				+SHorizontalBox::Slot()
				.VAlign(VAlign_Center)
				.FillWidth(1.f)
				.Padding(0,1,5,1)
				[
					SAssignNew(SearchAttributeBox, SSearchBox)
					.HintText(LOCTEXT("GameplayTagPicker_SearchBoxHint", "Search Gameplay Attributes"))
					.OnTextChanged(this, &SGSCAttributeListReferenceViewer::OnFilterTextChanged)
					.DelayChangeNotificationsWhileTyping(true)
				]
			]

			+SVerticalBox::Slot()
			.FillHeight(1)
			[
				SAssignNew(AttributesContainerWidget, SBorder)
				.BorderImage(FStyleDefaults::GetNoBrush())
				[
					SAssignNew(AttributeList, SListView<TSharedPtr<FGSCAttributeListReferenceViewerNode>>)
					.Visibility(EVisibility::Visible)
					.SelectionMode(ESelectionMode::Single)
					.ListItemsSource(&PropertyOptions)

					// Generates the actual widget for a tree item
					.OnGenerateRow(this, &SGSCAttributeListReferenceViewer::OnGenerateRowForAttributeViewer)

					// Find out when the user selects something in the tree
					.OnSelectionChanged_Static(&SGSCAttributeListReferenceViewer::OnAttributeSelectionChanged)
				]
			]
		];
	
	ChildSlot
	[
		// Populate the widget
		Picker
	];
}

void SGSCAttributeListReferenceViewer::OnFilterTextChanged(const FText& InFilterText)
{
	AttributeTextFilter->SetRawFilterText(InFilterText);
	SearchAttributeBox->SetError(AttributeTextFilter->GetFilterErrorText());

	UpdatePropertyOptions();

	AttributeList->RequestListRefresh();
}

TSharedPtr<SWidget> SGSCAttributeListReferenceViewer::GetWidgetToFocusOnOpen()
{
	return SearchAttributeBox;
}

void SGSCAttributeListReferenceViewer::UpdatePropertyOptions()
{
	using namespace UE::GASCompanionEditor::Private;
	
	PropertyOptions.Empty();

	// Gather all UAttribute classes
	for (TObjectIterator<UClass> ClassIt; ClassIt; ++ClassIt)
	{
		const UClass* Class = *ClassIt;

		if (IsValidAttributeClass(Class))
		{
			// Allow entire classes to be filtered globally
			if (Class->HasMetaData(TEXT("HideInDetailsView")))
			{
				continue;
			}

			for (TFieldIterator<FProperty> PropertyIt(Class, EFieldIteratorFlags::ExcludeSuper); PropertyIt; ++PropertyIt)
			{
				FProperty* Property = *PropertyIt;

				// if we have a search string and this doesn't match, don't show it
				if (AttributeTextFilter.IsValid() && !AttributeTextFilter->PassesFilter(*Property))
				{
					GSC_EDITOR_PLOG(Warning, TEXT("%s filtered"), *Property->GetName());
					continue;
				}

				// Allow properties to be filtered globally (never show up)
				if (Property->HasMetaData(TEXT("HideInDetailsView")))
				{
					continue;
				}

				// Only allow field of expected types
				FString CPPType = Property->GetCPPType();
				if (!IsValidCPPType(CPPType))
				{
					continue;
				}

				const FString AttributeName = FString::Printf(TEXT("%s.%s"), *GetAttributeClassName(Class), *Property->GetName());
				PropertyOptions.Add(MakeShared<FGSCAttributeListReferenceViewerNode>(Property, AttributeName));
			}
		}

		// UAbilitySystemComponent can add 'system' attributes
		if (Class->IsChildOf(UAbilitySystemComponent::StaticClass()) && !Class->ClassGeneratedBy)
		{
			for (TFieldIterator<FProperty> PropertyIt(Class, EFieldIteratorFlags::ExcludeSuper); PropertyIt; ++PropertyIt)
			{
				FProperty* Property = *PropertyIt;

				// SystemAttributes have to be explicitly tagged
				if (!Property->HasMetaData(TEXT("SystemGameplayAttribute")))
				{
					continue;
				}

				// if we have a search string and this doesn't match, don't show it
				if (AttributeTextFilter.IsValid() && !AttributeTextFilter->PassesFilter(*Property))
				{
					continue;
				}

				const FString AttributeName = FString::Printf(TEXT("%s.%s"), *Class->GetName(), *Property->GetName());
				PropertyOptions.Add(MakeShared<FGSCAttributeListReferenceViewerNode>(Property, AttributeName));
			}
		}
	}
}

TSharedRef<ITableRow> SGSCAttributeListReferenceViewer::OnGenerateRowForAttributeViewer(TSharedPtr<FGSCAttributeListReferenceViewerNode> InItem, const TSharedRef<STableViewBase>& InOwnerTable) const
{
	TSharedRef<SGSCAttributeListReferenceViewerItem> ReturnRow = SNew(SGSCAttributeListReferenceViewerItem, InOwnerTable)
		.HighlightText(SearchAttributeBox->GetText())
		.TextColor(FLinearColor(1.0f, 1.0f, 1.0f, 1.f))
		.AssociatedNode(InItem);

	return ReturnRow;
}

void SGSCAttributeListReferenceViewer::OnAttributeSelectionChanged(TSharedPtr<FGSCAttributeListReferenceViewerNode> InItem, ESelectInfo::Type SelectInfo)
{
	if (InItem.IsValid() && InItem->Attribute.IsValid())
	{
		TArray<FAssetIdentifier> AssetIdentifiers;
		const FName Name = FName(*FString::Printf(TEXT("%s.%s"), *InItem->Attribute->GetOwnerVariant().GetName(), *InItem->Attribute->GetName()));
		AssetIdentifiers.Add(FAssetIdentifier(FGameplayAttribute::StaticStruct(), Name));

		FReferenceViewerParams ReferenceViewerParams;
		ReferenceViewerParams.bShowDependencies = false;
		ReferenceViewerParams.FixAndHideSearchDepthLimit = 1;
		ReferenceViewerParams.bShowShowReferencesOptions = false;
		ReferenceViewerParams.bShowShowSearchableNames = false;
		FEditorDelegates::OnOpenReferenceViewer.Broadcast(AssetIdentifiers, ReferenceViewerParams);	
	}
}

#undef LOCTEXT_NAMESPACE
