{"FileVersion": 3, "Version": 1, "VersionName": "6.1.1", "FriendlyName": "GAS Companion", "Description": "Leveraging the power of the Gameplay Ability System, this plugin provides a starting template and a robust foundation to speed up the creation and development of GAS based projects.", "Category": "Gameplay", "CreatedBy": "<PERSON><PERSON> <mklabs>", "CreatedByURL": "https://mklabs.github.io", "DocsURL": "https://gascompanion.github.io", "MarketplaceURL": "com.epicgames.launcher://ue/marketplace/product/d83c6f34c3fb4b7092dde195c37c7413", "SupportURL": "https://github.com/GASCompanion/GASCompanion-Documentation/issues", "EngineVersion": "5.6.0", "CanContainContent": true, "Installed": true, "Modules": [{"Name": "GASCompanion", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}, {"Name": "GASCompanionDeveloper", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON>efault", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}, {"Name": "GASCompanionEditor", "Type": "Editor", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}], "Plugins": [{"Name": "GameplayAbilities", "Enabled": true}, {"Name": "ModularGameplay", "Enabled": true}, {"Name": "GameFeatures", "Enabled": true}, {"Name": "EnhancedInput", "Enabled": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Enabled": true}, {"Name": "Plug<PERSON><PERSON><PERSON><PERSON>", "Enabled": true}]}