[CoreRedirects]
+PropertyRedirects=(OldName="GSCGameplayAbility.bIgnoreAbilityCost", NewName="GSCGameplayAbility.bLooselyCheckAbilityCost")

; Deprecated classes
+ClassRedirects=(OldName="/Script/GASCompanion.GSCPlayerCharacter",NewName="/Script/GASCompanion.GSCModularCharacter")
+ClassRedirects=(OldName="/Script/GASCompanion.GSCAICharacter",NewName="/Script/GASCompanion.GSCModularCharacter")
+ClassRedirects=(OldName="/Script/GASCompanion.GSCDefaultPawn",NewName="/Script/GASCompanion.GSCModularDefaultPawn")
+ClassRedirects=(OldName="/Script/GASCompanion.GSCPlayerState",NewName="/Script/GASCompanion.GSCModularPlayerState")
+ClassRedirects=(OldName="/Script/GASCompanion.GSCGameModeBase",NewName="/Script/GASCompanion.GSCModularGameModeBase")
+ClassRedirects=(OldName="/Script/GASCompanion.GSCPlayerController",NewName="/Script/GASCompanion.GSCModularPlayerController")
+ClassRedirects=(OldName="/Script/GASCompanion.GSCHUD",NewName="/Script/Engine.HUD")

; Module ModularGASCompanion removal

; Gameplay Framework Actor classes
+ClassRedirects=(OldName="/Script/ModularGASCompanion.ModularCharacter",NewName="/Script/GASCompanion.GSCModularCharacter")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.ModularPawn",NewName="/Script/GASCompanion.GSCModularPawn")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.ModularDefaultPawn",NewName="/Script/GASCompanion.GSCModularDefaultPawn")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.ModularActor",NewName="/Script/GASCompanion.GSCModularActor")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.ModularAIController",NewName="/Script/GASCompanion.GSCModularAIController")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.ModularGameModeBase",NewName="/Script/GASCompanion.GSCModularGameModeBase")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.ModularGameMode",NewName="/Script/GASCompanion.GSCModularGameMode")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.ModularGameStateBase",NewName="/Script/GASCompanion.GSCModularGameStateBase")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.ModularGameState",NewName="/Script/GASCompanion.GSCModularGameState")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.ModularPlayerController",NewName="/Script/GASCompanion.GSCModularPlayerController")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.ModularPlayerState",NewName="/Script/GASCompanion.GSCModularPlayerState")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.ModularPlayerStateCharacter",NewName="/Script/GASCompanion.GSCModularPlayerStateCharacter")

; Components
+ClassRedirects=(OldName="/Script/ModularGASCompanion.MGCAbilitySystemComponent",NewName="/Script/GASCompanion.GSCAbilitySystemComponent")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.MGCPlayerControlsComponent",NewName="/Script/GASCompanion.GSCPlayerControlsComponent")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.MGCAbilityInputBindingComponent",NewName="/Script/GASCompanion.GSCAbilityInputBindingComponent")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.MGCLinkAnimLayersComponent",NewName="/Script/GASCompanion.GSCLinkAnimLayersComponent")

; Game Features Actions
+ClassRedirects=(OldName="/Script/ModularGASCompanion.MGCGameFeatureAction_AddAbilities",NewName="/Script/GASCompanion.GSCGameFeatureAction_AddAbilities")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.MGCGameFeatureAction_AddAnimLayers",NewName="/Script/GASCompanion.GSCGameFeatureAction_AddAnimLayers")
+ClassRedirects=(OldName="/Script/ModularGASCompanion.MGCGameFeatureAction_AddInputMappingContext",NewName="/Script/GASCompanion.GSCGameFeatureAction_AddInputMappingContext")

; Structs / Enums
+EnumRedirects=(OldName="/Script/ModularGASCompanion.EMGCAbilityTriggerEvent",NewName="/Script/GASCompanion.EGSCAbilityTriggerEvent")
+StructRedirects=(OldName="MGCAbilityInputMapping",NewName="/Script/GASCompanion.GSCAbilityInputMapping")
+StructRedirects=(OldName="MGCAttributeSetDefinition",NewName="/Script/GASCompanion.GSCAttributeSetDefinition")
+StructRedirects=(OldName="MGCAnimLayerEntry",NewName="/Script/GASCompanion.GSCAnimLayerEntry")
+StructRedirects=(OldName="MGCGameFeatureAbilityMapping",NewName="/Script/GASCompanion.GSCGameFeatureAbilityMapping")
+StructRedirects=(OldName="MGCGameFeatureAttributeSetMapping",NewName="/Script/GASCompanion.GSCGameFeatureAttributeSetMapping")
+StructRedirects=(OldName="MGCGameFeatureAbilitiesEntry",NewName="/Script/GASCompanion.GSCGameFeatureAbilitiesEntry")

; Misc
+ClassRedirects=(OldName="/Script/ModularGASCompanion.MGCConsoleManagerSubsystem",NewName="/Script/GASCompanion.GSCConsoleManagerSubsystem")
+FunctionRedirects=(OldName="MGCBlueprintFunctionLibrary.GetAbilityInputBindingComponent",NewName="GSCBlueprintFunctionLibrary.GetAbilityInputBindingComponent")
