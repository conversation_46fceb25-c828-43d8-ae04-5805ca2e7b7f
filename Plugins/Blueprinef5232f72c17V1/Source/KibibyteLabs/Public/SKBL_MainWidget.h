// Copyright 2025, Ki<PERSON>byte, All rights reserved

#pragma once
#pragma warning (disable : 4668)

#include "CoreMinimal.h"

#include "UObject/SavePackage.h"        

#include "Widgets/SCompoundWidget.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Input/SEditableTextBox.h"
#include "SlateOptMacros.h"
#include "Styling/SlateWidgetStyle.h"
#include "Styling/SlateStyle.h"
#include "Styling/SlateStyleRegistry.h"
#include "Styling/SlateBrush.h"

#include "SlateBasics.h"
#include "SlateExtras.h"

#include "Interfaces/IPluginManager.h"
#include "IImageWrapperModule.h"
#include "Misc/Paths.h"

#ifdef _WIN64
#include "Windows.h"
#include "dwmapi.h"
#include "wingdi.h"
#include "windef.h"
#include "winuser.h"
#endif

#include "HttpModule.h"
#include "Http.h"
#include "Json.h"
#include "JsonUtilities.h"

#include "KibibyteLabsStyle.h"

#include "Kismet/KismetRenderingLibrary.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Kismet/KismetStringLibrary.h"
#include "Kismet/KismetArrayLibrary.h"
#include "Kismet/KismetMathLibrary.h"
#include "Misc/FileHelper.h"
#include <string>

#include "Editor.h"
#include "EditorSubsystem.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "BlueprintEditor.h"
#include "Engine/LevelScriptBlueprint.h"
#include "Fonts/SlateFontInfo.h"

#include "IAssetTools.h"
#include "AssetToolsModule.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Engine/UserDefinedEnum.h"
#include "Factories/EnumFactory.h"
#include "EditorAssetLibrary.h"
#include "PackageTools.h"
#include "Kismet2/StructureEditorUtils.h"
#include "UserDefinedStructure/UserDefinedStructEditorData.h"
#include "FileHelpers.h"

#include "K2Node_EditablePinBase.h"
#include "Kismet2/Kismet2NameValidators.h"
#include "K2Node_Event.h"
#include "K2Node_FunctionEntry.h"
#include "K2Node_CustomEvent.h"
#include "K2Node_FunctionResult.h"

#include "HAL/FileManager.h"
#include "Misc/Paths.h"
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"

#include "K2Node_IfThenElse.h"
#include "K2Node_MacroInstance.h"
#include "K2Node_Switch.h"
#include "K2Node_SwitchInteger.h"
#include "K2Node_SwitchString.h"
#include "K2Node_SwitchEnum.h"
#include "K2Node_Select.h"

#include "BlueprintFunctionNodeSpawner.h"
#include "K2Node_ExecutionSequence.h"
#include "K2Node_VariableGet.h"
#include "K2Node_VariableSet.h"
#include "K2Node_CallFunction.h"
#include "K2Node_CallArrayFunction.h"
#include "K2Node_MakeStruct.h"
#include "K2Node_BreakStruct.h"
#include "K2Node_EnumLiteral.h"
#include "K2Node_EnumEquality.h"

#include "KibibyteLabs_Settings.h"

#include "Misc/Base64.h"
#include "Containers/StringConv.h" 

#include "GameFramework/SaveGame.h"
#include "HAL/PlatformApplicationMisc.h"

#include "Editor/GraphEditor/Public/SGraphNode.h"
#include <functional>    

class UKibibyteLabs_Settings;


enum EBP_HttpMethod : uint8
{
	Get,
	Post,
	Put,
	Delete
};

enum EBP_HttpContentType : uint8
{
	None,
	Form_Urlencoded,
	Application_Json,
	Application_XML,
	Application_JS,
	Text_Plain,
	Text_Html
};

enum EBP_HttpCallForwarding : uint8
{
	ChatRequest,
	ExplainRequest,
	AutoAuth,
	FirstTimeAuth,
	StartupUpdateCheck,
	GetUpdateInformation,
	DownloadUpdate,
};

struct FMapHeader
{
	TMap<FString, FString> Header;
};

struct FMapQuery
{
	TMap<FString, FString> Query;
};

struct FLLMRequestData
{
	FString Url;
	FString Body;
	FMapHeader Headers;
};

struct FChatMessage
{
	int ResponseCode;
	FString Message;              
	bool bIsUserMessage;
	bool bIsSpecial;       
	UBlueprint* Blueprint;    
	UEdGraph* Graph;          
	UEdGraphNode* Node;       

	FChatMessage(int InResponseCode, const FString& InMessage, bool InIsUserMessage)
		: ResponseCode(InResponseCode), Message(InMessage), bIsUserMessage(InIsUserMessage),
		bIsSpecial(false), Blueprint(nullptr), Graph(nullptr), Node(nullptr) {
	}

	FChatMessage(UBlueprint* InBlueprint, UEdGraph* InGraph, UEdGraphNode* InNode)
		: ResponseCode(0), Message(TEXT("Done!")), bIsUserMessage(false),
		bIsSpecial(true), Blueprint(InBlueprint), Graph(InGraph), Node(InNode) {
	}

	FChatMessage() : ResponseCode(0), Message(TEXT("")), bIsUserMessage(false), bIsSpecial(false), Blueprint(nullptr), Graph(nullptr), Node(nullptr) {}
};

class KIBIBYTELABS_API SKBL_MainWidget : public SCompoundWidget
{
	friend class KBL_Compiler;
	friend class KBL_Unzipper;

public:
	SLATE_BEGIN_ARGS(SKBL_MainWidget) {}
	SLATE_END_ARGS()


	void Construct(const FArguments& InArgs);

	FVector2D CurrentSize;
	FVector2D CurrentPosition;
	bool CurrentlyClosed = false;
	HWND EditorHandle = nullptr;

	void LaunchNewInputArgument(FString InputArgument);          
	void SetNewBlueprintReference(UBlueprint* NewBlueprint, bool IsDirectReference);       

	void OnAPITextChanged(const FText& Text);

	TSharedPtr<SWidgetSwitcher> ChatWidgetSwitcher;
	void OnWindowSizeChanged(FVector2D NewSize);
	void RebuildVisibleMessages();
private:
	TArray<FChatMessage> VisibleChatMessages;

	void OnChatTextChanged(const FText& Text);
	void OnBigChatTextChanged(const FText& Text);
	FReply OnChatKeyDown(const FGeometry& MyGeometry, const FKeyEvent& InKeyEvent);
	FReply OnApiKeyDown(const FGeometry& MyGeometry, const FKeyEvent& InKeyEvent);
	FReply OnDirectBlueprintClicked();
	FReply OnContentBrowerBlueprintClicked();
	FReply OnAPIKeySubmitted();
	FString TemporaryAPIKey = FString();
	FReply OnGeminiSubmitted();
	FReply OnOpenAISubmitted();
	FString GetModelName(const UKibibyteLabs_Settings* Settings);
	FLLMRequestData BuildRequestData(const UKibibyteLabs_Settings* Settings, const TArray<FString>& MessageHistory, const FString& SystemPrompt);
	FReply OnSendClicked();
	void OnExplainClicked(FString ContextJson, bool bIsCodeBlock);
	void OnDiagnoseClicked(FString ContextJson);
	FString RemoveAllGuidTags(const FString& Input);
	FReply OnExpandChatClicked();
	FReply OnClosedClicked();
	void OnClosedPressed();      
	void OnClosedReleased();      
	FReply OnBackToNormalChatClicked();

	FString EscapeStringForJson(const FString& Input);

	void CloseWindow();
	TArray<uint8> XORBytes(const TArray<uint8>& Data, const TArray<uint8>& KeyBytes);
	FString EncryptString(const FString& PlainText, const FString& Key);
	void ActuallyDestroyWindow();
	void CheckHoldDuration();
	void HandleInitialize();


	void HttpRequestEvent(EBP_HttpCallForwarding ForwardingSytem, const FString& Url, const EBP_HttpMethod Method, const EBP_HttpContentType ContentType, const FMapHeader Header, const FMapQuery Query, const FString& Body);
	void OnHttpResponse(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful);

	FString ChopStringByTripleQuotes(const FString& InputString);
	FString GetMethod(const EBP_HttpMethod Method);
	FString GetContentType(const EBP_HttpContentType ContentType);

	bool GetSpecificJsonKeys(const FString& JsonString, const TArray<FString>& Keys, TArray<FString>& Contents);

	void AddChatMessage(int ResponseCode, const FString& Message, bool IsUserMessage);

	void ReportGenerationIssue(const FString& CompilerIssueDetails);

	FReply OnIssueMessageButtonClicked(FString MessageToCopy);

	void AddIssueNotificationMessageUI(const FString& IssueDetailsTooltip, const FString& TimeString);

	FString ConvertMarkdownToRichText(const FString& InText);

	FReply OnSpecialButtonClicked(UBlueprint* Blueprint, UEdGraph* Graph, UEdGraphNode* Node);
	FReply OnAPIButtonClicked();

	void TestFillChat();

	FReply OnNewChatButtonClicked();

	void AddSpecialMessage(UBlueprint* Blueprint, UEdGraph* Graph, UEdGraphNode* Node);

	void AddMessageToHistory(FString NewItem);

	static FString KB_RemoveStartAndEndWhitespaceMainWidget(FString InString);
	FString LastRawAssistantMessageForIssue;        
	bool bHasShownIssueForCurrentOperation = false;

	TSharedPtr<SButton> CS_CloseButton;
	TSharedPtr<SButton> CS_NewChatButton;
	TSharedPtr<SButton> CS_ExpandButton;
	TSharedPtr<SButton> CS_SendButton;           
	TSharedPtr<SButton> CS_SendAPIButton;           
	TSharedPtr<SMultiLineEditableTextBox> CS_ChatTextBox;
	TSharedPtr<SMultiLineEditableTextBox> CS_ApiTextBox;
	TSharedPtr<STextBlock> CS_CurrentDirectContextText;
	TSharedPtr<STextBlock> CS_CurrentContentBrowserContextText;
	TSharedPtr<SButton> CS_CurrentDirectContextButton;
	TSharedPtr<SButton> CS_CurrentContentBrowserContextButton;
	TSharedPtr<SScrollBox> ChatScrollBox;
	TSharedPtr<SVerticalBox> ChatVBox;
	TSharedPtr<SButton> BCS_CloseButton;
	TSharedPtr<SButton> BCS_BackButton;
	TSharedPtr<SButton> BCS_SendButton;
	TSharedPtr<SMultiLineEditableTextBox> BCS_BigChatTextBox;
	TSharedPtr<SWidgetSwitcher> MainWidgetSwitcher;
	TArray<FString> AllMessagesSent = {};
	int HowManyMessagesToRemember = 4;      

	FString CurrentVersion = FString();
	bool bIsButtonPressed;
	FTimerHandle TimerHandle;
	FTimerDelegate TimerDelegate;
	const float HoldDuration = 1.0f;    

	EBP_HttpCallForwarding CurrentCallForwarding;
	FText CurrentMessage;
	FText CurrentApiSetup;

	UBlueprint* CurrentDirectBlueprint;
	FString CurrentDirectBlueprintPath;    
	UBlueprint* CurrentContentBrowserBlueprint;
	FString CurrentContentBrowserBlueprintPath;    
	UBlueprint* ActuallyCurrentSelectedBlueprint;
	UEdGraph* CurrentGraph;
	UEdGraphNode* CurrentNode;

};

class FCustomMainWidgetStyle   
{
public:
	static void Initialize();
	static void Shutdown();
	static const ISlateStyle& Get();
	static FName GetStyleSetName();

private:
	static TSharedRef<FSlateStyleSet> Create();
	static void CreateAndSetImageBrush(FSlateStyleSet& Style, const FString& ImageName, const FName& BrushName);

	static TSharedPtr<FSlateStyleSet> CustomStyleInstance;


	static TSharedPtr<FSlateStyleSet> RichTextDecoratorStyleSet;
};

class KBL_Compiler
{
    friend class SKBL_MainWidget;          
    
private:
	enum EVariablePinTypeIDK
	{
		VIDK_Single,
		VIDK_Array,
		VIDK_Set,
		VIDK_Map
	};
	enum EKB_BPAI_VariableType
	{
		KB_BPAI_VariableType_Boolean,
		KB_BPAI_VariableType_Byte,
		KB_BPAI_VariableType_Integer,
		KB_BPAI_VariableType_Integer64,
		KB_BPAI_VariableType_Float,
		KB_BPAI_VariableType_Name,
		KB_BPAI_VariableType_String,
		KB_BPAI_VariableType_Text,
		KB_BPAI_VariableType_Vector,
		KB_BPAI_VariableType_Rotator,
		KB_BPAI_VariableType_Transform,
		KB_BPAI_VariableType_Object,
		KB_BPAI_VariableType_Texture2D,
		KB_BPAI_VariableType_Color,
		KB_BPAI_VariableType_Actor,
		KB_BPAI_VariableType_Vector2D,
		KB_BPAI_VariableType_LinearColor,
		KB_BPAI_VariableType_SlateColor,
		KB_BPAI_VariableType_IntPoint,
		KB_BPAI_VariableType_DateTime,
		KB_BPAI_VariableType_Timespan,    
		KB_BPAI_VariableType_IntVector,
		KB_BPAI_VariableType_IntVector4,
		KB_BPAI_VariableType_SaveGame,
	};
	enum EKB_BPAI_PinDirection
	{
		KB_BPAI_PinDirection_In,
		KB_BPAI_PinDirection_Out,
	};

	enum EK2NodeType                   
	{
		VT_if,                        
		VT_for,
		VT_foreach,
		VT_while,
		VT_do,
		VT_switchString,
		VT_switchInt,
		VT_switchEnum,
	};

	enum EVariableType
	{
		Global,
		Local
	};

	enum EVariableNodeType
	{
		Get,
		Set
	};

enum EArithmeticOperation
	{
		Add,
		Subtract,
		Multiply,
		Divide
	};

enum EArithmeticDataType               
	{
		Integer,
		Float,
		Byte,
		Vector,
		Vector2D,
		IntPoint,
		Rotator,
		Quat,
		Transform,
		LinearColor,
		DateTime,
		Timespan,
		Plane,
		IntVector,
		Integer64,
		Vector4,
	};
	struct FAllInputAndOutputVariable          
	{
		FString Name = FString();
		FString Type = FString();
		EVariablePinTypeIDK VarType = EVariablePinTypeIDK::VIDK_Single;
	};
	struct FCodeSection
	{
		FString Type = FString();
		FString Content = FString();
	};
	struct FEnumInfo
	{
		FString Name;
		TArray<FString> Members;
		TArray<FString> UMetas;            
		FString UEnumDeclaration;        
	};
	struct FStructInfo
	{
		FString Name;
		TArray<FString> Members;
		TArray<FString> UProperties;                 
		FString UStructDeclaration;              
	};
	struct FGlobalAddtitionalEnum            
	{
		FString InCodeName;          
		FString RealName;        
		FString AssetPath;        
		TArray<FString> Members;      
	};
	struct FGlobalAdditionalStruct            
	{
		FString InCodeName;          
		FString RealName;        
		FString AssetPath;        
		TArray<FString> Members;          
	};
	struct FKB_FunctionPinInformations   
	{
		FName Name = FName();
		TEnumAsByte<EKB_BPAI_VariableType> Type = EKB_BPAI_VariableType::KB_BPAI_VariableType_Boolean;
		TEnumAsByte<EVariablePinTypeIDK> VarType = EVariablePinTypeIDK::VIDK_Single;
		bool UseCustomVarType = false;
		FString CustomVarTypeName = FString();
		FString CustomVarTypePath = FString();
	};
	
	struct FKB_PinTypeInformations   
	{
		int Index = 0;
		FName Name = FName();
		FName Category = FName();
		FName SubCategory = FName();
		FString SubCategoryObject = FString();
		EKB_BPAI_PinDirection PinDirection = EKB_BPAI_PinDirection::KB_BPAI_PinDirection_In;  
		bool IsHidden = false;
	};

	struct FKB_TempVarsUntilNow       
	{
		TArray<FString> TempVarNames = {};
		TArray<FKB_PinTypeInformations> TempVarPins = {};
		TArray<FString> TempVarTypes = {};
	};

	struct FKB_SGlobalWhileSections
	{
		TArray<FString> GlobalCodes = {};
	};

	struct FCustomEnumOperandInfo
	{
		bool bIsCustomEnum = false;
		UEnum* EnumAsset = nullptr;
		FString EnumAssetPath;
		FString EnumInCodeName;   
		FString EnumValueIfLiteral;        
		bool bIsLiteral = false;
		FString OriginalOperandString;

		FCustomEnumOperandInfo() : bIsCustomEnum(false), EnumAsset(nullptr), bIsLiteral(false) {}
	};

	bool PrepareCompiler(FString InputSourceString);
	bool StartCompiler(FString InputSourceString);
	void AfterProcessCurrentZerothLevelCodeStuff();
	void ProcessQueue();
	void ProcessCurrentZerothLevelCode(FString CurrentZerothLevelCode, FKB_PinTypeInformations StartingOutExecPin, FKB_TempVarsUntilNow TempVarsUntilNow);
	void CompilerWordLoop(TArray<FString> InArray);

	bool IsRhsEffectivelyEmptyOrZeroDefault(const FString& RhsValueOriginal, const FString& ExpectedLhsTypeName);

	UEdGraphNode* KB_BPAI_GetFunctionNode(UEdGraph* FunctionGraph);

	static TArray<FCodeSection> SplitUnrealCode(const FString& SourceCode);
	static FEnumInfo ExtractEnumInfo(const FString& EnumContent);
	static UUserDefinedEnum* CreateEnumAsset(const FString& EnumName, const TArray<FString>& EnumValues, const FString& PackagePath, bool& bOutSuccess, FString& NewName);
	static FStructInfo ExtractStructInfo(const FString& StructContent);
	static UUserDefinedStruct* CreateStructAssetTest3(FString AssetPath, FString StructName, const TArray<FKB_FunctionPinInformations>& Members, bool& bOutSuccess, FString& OutInfoMessage, FString& NewName);
	void NEWDataFromFunction(const FString& SourceStringInput, FString& OutFunctionName, TArray<FString>& OutInputs, TArray<FString>& OutOutputs);
	bool KB_BPAI_CreateFunction(UBlueprint* BlueprintRef, FString FunctionName, TArray<FKB_FunctionPinInformations> InPins, TArray<FKB_FunctionPinInformations> OutPins, int& FunctionGraphIndex);      
	bool SafeFindUniqueKismetName(UBlueprint* InBlueprint, const FString& InBaseName, FName& ReturnName);

	bool KBL_SpawnMacroNode(FName MacroName, bool AutoFindPosition, FVector2D LocationOfNode, int& NodeIndex);
	int AddMacroNode(FName MacroName, UEdGraph* LocalGraph, FVector2D NodePosition);
	UBlueprint* KBL_GetStandardMacrosLibrary();
	UEdGraph* KBL_FindMacroGraph(UBlueprint* Blueprint, const FName MacroName);
	bool KBL_CreateMacroInstanceNode(UEdGraph* LocalGraph, UEdGraph* MacroGraph, FVector2D NodePosition);

	bool KB_BPAI_SpawnNode(UEdGraph* LocalGraph, FName NameOfFunction, UClass* ClassOfFunction, bool AutoFindPosition, FVector2D LocationOfFunction, int& NodeIndex);

	bool SpawnMathNode(UEdGraph* LocalGraph, EArithmeticOperation Operation, EArithmeticDataType DataType, bool AutoFindPosition, FVector2D LocationOfNode, int32& NodeIndex);

	bool KBL_SpawnSequenceNode(UEdGraph* LocalGraph, bool AutoFindPosition, FVector2D LocationOfNode, int& NodeIndex);

	bool KBL_SpawnK2Node(UEdGraph* LocalGraph, EK2NodeType NodeType, bool AutoFindPosition, FVector2D LocationOfNode, int& NodeIndex);

	bool KBL_SpawnSelectNode(UEdGraph* LocalGraph, bool AutoFindPosition, FVector2D LocationOfNode, int& NodeIndex);

	bool KBL_SpawnSelectNode2(UEdGraph* LocalGraph, bool AutoFindPosition, FVector2D LocationOfNode, int& NodeIndex, FKB_PinTypeInformations PinTypeInfo);

	bool KBL_SpawnEnumSwitch(UEdGraph* LocalGraph, FString EnumPath, bool AutoFindPosition, FVector2D LocationOfNode, int& NodeIndex);

	bool KB_BPAI_SpawnStructNode(UEdGraph* LocalGraph, UScriptStruct* StructType, bool bMakeStruct, bool AutoFindPosition, FVector2D LocationOfNode, int& NodeIndex);

	void UpdateArrayWildcardNodes();
	void UpdateBooleanOperatorNodes();
	void UpdateArrayNodeWildcards(int NodeIndex);

	bool IsNodeExecution(UEdGraphNode* Node);

	bool IsNodePure(UEdGraphNode* Node);

	FVector2D GetAssumedNodeSizeWithPadding();

	void FormatNodes(UEdGraph* LocalGraph);

	FVector2D GetNodeSize(UEdGraphNode* Node);

	UScriptStruct* GetStructByPath(const FString& AssetPath);

	bool KBL_SpawnK2VarNode(UEdGraph* LocalGraph, FName VarName, bool AutoDetectIfLocalOrGlobal, EVariableType VarBoundaries, EVariableNodeType NodeType, bool AutoFindPosition, FVector2D LocationOfNode, int& NodeIndex);
	bool KBL_DoesVariableExist(UEdGraph* LocalGraph, FName VariableName, bool& IsLocalVar, EKB_BPAI_VariableType& VarType);
	void KBL_NomalVarConversion(EKB_BPAI_VariableType VarType, UStruct*& ReturnStruct);

	static TArray<FBPVariableDescription> GetLocalVariablesForGraph(const UEdGraph* Graph);
	static TArray<FBPVariableDescription> GetGlobalVariablesForBlueprint(const UBlueprint* Blueprint);
	EKB_BPAI_VariableType KBL_RevertedPinVarConversion(FName PinCategory, FName PinSubCategory, TWeakObjectPtr<UObject> PinSubObject);

	bool KB_BPAI_CreateLocalVariable(UBlueprint* Blueprint, UEdGraph* LocalGraph, FKB_FunctionPinInformations Var, int& VariableIndex);

	bool KB_BPAI_GetNodePins(int NodeIndex, bool ExcludeInvisible, TArray<FKB_PinTypeInformations>& InPinInformations, TArray<FKB_PinTypeInformations>& OutPinInformations);
	bool IsNodePure(int NodeIndex);
	bool KB_BPAI_SetPinValue(int PinIndex, const FString& Value);
	bool KB_BPAI_ConnectPinsNEW(FKB_PinTypeInformations OutPin, FKB_PinTypeInformations InPin);
	
	bool KB_BPAI_SmartConnectSpecificNodePins(int NodeIndex1, int OutPinIndexNode1, int NodeIndex2, int InPinIndexNode2, bool ExcludeInvisibleForBoth);
	bool KB_BPAI_SmartConnectSpecificNodeOutOnly(int NodeIndex1, int OutPinIndexNode1, bool ExcludeInvisibleForOut, FKB_PinTypeInformations InPin);
	bool KB_BPAI_SmartConnectSpecificNodeInOnly(FKB_PinTypeInformations OutPin, int NodeIndex2, int InPinIndexNode2, bool ExcludeInvisibleForIn);
	FKB_PinTypeInformations KB_BPAI_SmartGetNodeInPin(int NodeIndex, int InPinIndexToGet, bool ExcludeInvisible);
	FKB_PinTypeInformations KB_BPAI_SmartGetNodeOutPin(int NodeIndex, int OutPinIndexToGet, bool ExcludeInvisible);
	
	bool KB_BPAI_SmartEasyConnectWithConversionSpecificNodePins(int NodeIndex1, int OutPinIndexNode1, int NodeIndex2, int InPinIndexNode2, bool ExcludeInvisibleForBoth, int DebugNumber);
	bool KB_BPAI_SmartEasyConnectWithConversionSpecificNodeOutOnly(int NodeIndex1, int OutPinIndexNode1, bool ExcludeInvisibleForOut, FKB_PinTypeInformations InPin, int DebugNumber);
	bool KB_BPAI_SmartEasyConnectWithConversionSpecificNodeInOnly(FKB_PinTypeInformations OutPin, int NodeIndex2, int InPinIndexNode2, bool ExcludeInvisibleForIn, int DebugNumber);

	static bool IsValidMarkerIndexContent(const FString& IndexContent);
	static bool TryExtractCoreMarkerPattern(const FString& TrimmedText, FString& OutCorePattern);
	static FString MoveUnrealMarkerUpIfAlone(const FString& InputString);

	bool ConvertClassAndFunctionToSpawnableCommandForWithoutBracketsOnly(FString In, FString& ActualClass, FString& ActualName, bool& IsPureFunction);
	bool ContainsIsolatedWord(const FString& Text, const FString& Word);
	bool ConvertArrayCallsToSpawnableCommand(FString In, FString& ActualName, bool& IsPureFunction, int& ArrayInPinIndex, bool& HasExecPins, int& OutputPin, int& OtherInPin);
	static void KBL_PinVarConversionLocal(EKB_BPAI_VariableType VarType, FName& PinCategory, FName& PinSubCategory, TWeakObjectPtr<UObject>& PinSubObject);
	static TArray<FKB_FunctionPinInformations> VariableStringArrayToPinArray(TArray<FString> InVarString);
	static bool GetCurrentMathEquasionSymbolType(FString InString, EArithmeticOperation& ReturnType);    
	static EKB_BPAI_VariableType VariableNameToVariableEnumConverter(FString InString);
	EArithmeticDataType VariableNameToSomeOtherEnumConverter(FString VarName);
	EArithmeticDataType VariableNameToSomeOtherEnumConverterTempForMath(FString VarName);
	bool SplitIndependentOfMathSymbol(FString InString, FString& LeftOrFull, FString& Right);     
	void StringToEnumConverter(TArray<FString> Inputs, TArray<FString> Outputs, TArray<FKB_FunctionPinInformations>& LocalInPins, TArray<FKB_FunctionPinInformations>& LocalOutPins);
	    void StringToEnumConverterHelper(FString InputString, EKB_BPAI_VariableType& CurrentVarType, EVariablePinTypeIDK& CurrentVarTypeIDK, bool& IsCustomEnum, bool& IsCustomStruct, int& WhichCustomThingIndex);
	bool KB_BPAI_GetFunctionPins(UEdGraph* Graph, TArray<FKB_PinTypeInformations>& InPinInformations, TArray<FKB_PinTypeInformations>& OutPinInformations);
	bool KB_BPAI_BreakAllPinConnections(int PinIndex);

	bool GetUObjectClassByName(const FString& ClassName, UClass*& OutClass);

	static TArray<FString> ParseStringIntoLines(const FString& InputString);          
	static void ReplaceComments(const FString& InCode, FString& OutModifiedCode, TArray<FString>& OutExtractedComments);
	static void ExtractStringLiterals(const FString& InText, FString& OutModifiedText, TArray<FString>& OutExtractedStrings);
	static TArray<FString> ConvertStringBufferToLines(const FString& Buffer);
	static void UnnestCodeRecursive(const TArray<FString>& InputCodeLines, TArray<FString>& BaseLevelOutputLinesCollector, TArray<FString>& OutAllUnnestedBlocks, int32& InOutNextBlockId);
	static void UnnestCode(const TArray<FString>& InputCodeLines, FString& OutProcessedBaseCode, TArray<FString>& OutAllUnnestedBlocks);
	static void NestedCodeUnnester(TArray<FString> EachLine, FString& ReturnValue, TArray<FString>& NavigateToArray1);
	static FString CleanUpCodeAdvanced(FString SourceString);
	static void INLINECODEUNNESTER(FString InEquasion, FString& SETInput, TArray<FString>& InputEbenen);
	void ConvertLocalIndexLineAndArrayToGLobal(FString LINE, TArray<FString> ARRAY, FString& GLOBALLINE, TArray<FString>& GLOBALARRAY);
	static TArray<FString> LittleInlineCodePreparerForArray(TArray<FString> In);
	static FString LittleInlineCodePreparer(FString In);
	FString PrepareCodeForCompiler(FString InSourceString);
	static FString SmartReplace(FString InSourceString, FString From, FString To, TArray<FString> Not);
	static void CautiousReplaceInternalSplitter(FString SourceString, FString From, TArray<FString> Not, FString& Split1, TArray<FString>& CutOutArray);
	static TArray<FString> CautiousReplaceInternalSplitterInternalSorter(TArray<FString> InArray);
	static TArray<int> KB_I1_Sort(TArray<int> InIntegerArray, bool ReturnReversed, TArray<int>& NewIndexOrder);      
	FString RealMultiplySymbolReplace(FString InSourceString);
	static TArray<int> FindNumberOfInstancesOfSymbol(FString SourceString, FString SymbolToSearchFor);
	static FString KB_FMP_S_GetSubstringByStrings(FString InString, FString StartString, int StartStringOffset, FString EndString, int EndStringOffset);
	static FString CleanUpCode(FString SourceString);
	static void SeperateInputsAndOutputs(TArray<FString> InArray, FString& Inputs, FString& Outputs);
	static FString KB_FMP_S_RemoveDoubleSpaces(FString InString);
	static FString KB_RemoveStartWhitespace(FString InString);
	static FString KB_RemoveEndWhitespace(FString InString);
	static FString KB_RemoveStartAndEndWhitespace(FString InString);
	static FString RemoveFunctionDeclerationAndSurroundingBrackets(FString InSource);
	static int CountCharacterInString(FString InputString, FString CharacterToCount);
	static FString JoinMarkedLines(const FString& InString);
	static bool KB_S_ContainsNumeric(FString String);
	static void SplitStructMemberArrayToTypeAndName(TArray<FString> InCustomStructMemberArray, TArray<FString>& Type, TArray<FString>& Name);
	static void BreakdownEquation(const FString& Equation, FString& OutBaseString, TArray<FString>& OutSubEquations);
	static bool FindFirstOperation(const FString& Equation, FString& OutBase, FString& OutLeft, FString& OutRight);
	void SplitStringByPlusSigns(const FString& Input, TArray<FString>& OutParts);
	void AddInputPinToConcatNode(int NodeIndex);
	      bool AdditionalEnumGatherFunctionByInCodeName(FString ItemToFind, bool InCodeFriendlyName, int& FindInteger);    
	      bool AdditionalStructGatherFunctionByInCodeName(FString ItemToFind, bool InCodeFriendlyName, int& FindInteger);    
	void GetCustomEnumOrStructValue(bool IsCustomAssetEnum, int WhichCustomAssetIndex, FString& Name, FString& Path);    
	void BoolSetterFunction(bool WasVariableDecleration, bool WasFunctionCall, bool WasMidCodeVariableIdentifier, bool InWasOnTopCustomVarDecleration);
	bool TryInsertIsValidForObjectToBool(UEdGraph* TargetGraph, const FKB_PinTypeInformations& SourceObjectPin, const FString& SourceObjectTypeString, const FKB_PinTypeInformations& TargetBooleanPin);
	bool IsObjectPointerType(const FString& TypeString);
	FCustomEnumOperandInfo GetCustomEnumOperandInfo(const FString& OperandString);
	void ProcessEnumOperand(const FCustomEnumOperandInfo& OperandInfo, FKB_PinTypeInformations TargetPin);
	void JustAnotherFunctionToSpawnStuffPureOnly(FString ItemToFind, FKB_PinTypeInformations InPin);
	bool SpawnBooleanOperatorNode(UEdGraph* LocalGraph, FString Operation, bool AutoFindPosition, FVector2D LocationOfNode, int32& NodeIndex, UEnum* EnumToCompare = nullptr);
	bool GetCurrentBooleanOperatorType(FString InString, FString& ReturnSymbol);
	bool SplitIndependentOfBooleanSymbol(FString InString, FString& LeftOrFull, FString& Right);
	void BOOLOPERATORPROCESSOR(FString InExpression, FString& OutputExpression, TArray<FString>& OperatorExpressions);
	bool FindInputByName(TArray<FKB_PinTypeInformations> InInputs, FName Name, FKB_PinTypeInformations& Pin);
	void SpawnMathInputTree(UEdGraph* LocalGraph, FString Equation, int StartNodeIndex, int StartNodePinIndex, bool UseInPinInsteadAsStart, FKB_PinTypeInformations InPin);
	void PreparerChooseStructOrSpawnGet(FString InLocalVarName, bool UsePreDefinedPinForStructs, FKB_PinTypeInformations PreDefinedPinForStructs, FString OptionalTypeForStructs, FString& ReturnValue, FKB_PinTypeInformations& ReturnValue2);
	void HandleSetOperationWithEverythingElse(FString InLocalVarName, FString InFullLine);
	void HandlePinInputComplete(UEdGraph* LocalGraph, FKB_PinTypeInformations InputPin, FString InLocalVarName, FString InFullLine, int StartNodeIndex, int StartNodePinIndex, FString OptionalTypeForStruct, bool UsePreDefinedPinForStructs, FKB_PinTypeInformations PreDefinedPinForStructs);
	void MathSpawnerForHandleSet(FKB_PinTypeInformations InPin, FString Selection, FKB_PinTypeInformations OutPin, FString ItemToFind, EArithmeticOperation Operation);
	void SpawnAndConnectHelperNodeForUnusualSetMath(FString ItemToFind, FKB_PinTypeInformations OutPin, FKB_PinTypeInformations InPin, EArithmeticOperation Operation, EArithmeticDataType DataType);
	bool OptionalMathUppbreakerAndItsMathDecider(FString InString, FKB_PinTypeInformations InPinInfo, TArray<FString> WholeArray, FString& MathOutput);
	void AddToMathQueue(FKB_PinTypeInformations Pin, FString Content);
	bool IsStringType(const FString& VarName);
	void EasyMathConnector(UEdGraph* LocalGraph, int NodeIndex, FKB_PinTypeInformations InPin, FKB_PinTypeInformations OutPin, FString ItemToFind);
	void EasyPinConnectorWithAutoConversion(UEdGraph* LocalGraph, FKB_PinTypeInformations OutPin, FKB_PinTypeInformations InPin, int WhichDEBUG);
	void SpawnConverterAndConnect(UEdGraph* LocalGraph, FName NameOfFunction, UClass* ClassOfFunction, FKB_PinTypeInformations OutPin, FKB_PinTypeInformations InPin);
	bool SpawnDotFunction(FString VarOrInputName, FString FunctionName, FKB_PinTypeInformations InPin);
	void EasyConnectExecPinOnTheFly(int NodeIndex, int OutWhereToContinuePin);
	void TryToConnectExecPinsOnTheFly(int OtherNodeIndex, FKB_PinTypeInformations OutPinInformation);
	static void ParseSwitchCaseCode(const FString& CodeString, TArray<FString>& OutCases, TArray<FString>& OutCodeBlocks);
	bool KBL_SpawnEnumLiteralNode(UEdGraph* TargetGraph, UUserDefinedEnum* EnumAsset, const FString& EnumValueName, FVector2D LocationOfNode, int32& OutNodeIndex);
	static void ParseEnumCaseString(const FString& CaseString, FString& OutEnumType, FString& OutEnumValue);
	FString DataExtractor(FString InString, FString Symbol);
	void CallOnGenerationIssue(FString InString);
	void CallOnDebug(FString InString);
	TArray<FString> GetAllInputAndOutputVariablesNames();
	TArray<FString> GetAllInputAndOutputVariablesTypes();  
	TArray<FString> GetAllFunctionReturnPinNames();

	FKB_PinTypeInformations FromPreviousIfStatement;

	SKBL_MainWidget* MainWidgetInstance;
	FString SourceString = FString();       
	TArray<FGlobalAddtitionalEnum> GlobalAdditionalEnumArray = {};
	TArray<FGlobalAdditionalStruct> GlobalAdditionalStructArray = {};
	TArray<FAllInputAndOutputVariable> AllInputAndOutputVariables = {};
	TArray<FString> AllLocalVariables = {};     
	TArray<FString> AllLocalVariablesTypes = {};          
	TArray<EVariablePinTypeIDK> AllLocalVariablesVarTypes = {};     
	TArray<FKB_PinTypeInformations> FunctionInputPins = {};
	TArray<FKB_PinTypeInformations> FunctionReturnPins = {};
	TArray<FString> FunctionReturnPinsWhichOne = {};
	TArray<FString> FunctionReturnPinsContent = {};
	UBlueprint* CurrentSelectedBlueprint = nullptr;
	TArray<UBlueprint*> LocalBlueprintList = {};
	TArray<UEdGraph*> LocalFunctionGraphList = {};
	int LocalCurrentFunctionGraphIndex = 0;
	TArray<UEdGraphNode*> NodeList = {};
	TArray<UEdGraphPin*> PinList = {};

	TArray<FString> NewCommentArrayC, NewStringLiteralArrayS, CurlyBracketArrayU = {};
	TArray<FString> RoundBracketsArrayG = {};
	TArray<FString> InputsInBetween = {};
	TArray<FKB_SGlobalWhileSections> SGlobalWhileSections = {};

	FKB_PinTypeInformations CurrentMainExecPinRoute;
	FKB_PinTypeInformations CurrentMainExecPinRouteForReturnNodePin;
	FString UnprocessedLine = FString();
	FString ProcessedLine = FString();
	int CurrentOutIndex = 0;

	bool PreWasVariableDecleration = false;
	bool PreWasFunctionCall = false;
	bool PreWasMidCodeVariableIdentifier = false;
	bool WasOnTopCustomVarDecleration = false;
	FString PreVarType = FString();
	TArray<FString> OutArray = {};
	FString CurrentLineElement = FString();
	int CurrentWorldIndex = 0;
	EVariablePinTypeIDK CurrentLineLocalVarVarTypeIDKDecleration = EVariablePinTypeIDK::VIDK_Single;

	const TArray<FString> AllVariableConstants = {
	TEXT("bool"), TEXT("uint8"), TEXT("int"), TEXT("int64"), TEXT("float"),
	TEXT("double"), TEXT("FName"), TEXT("FString"), TEXT("FText"), TEXT("FVector"),
	TEXT("FRotator"), TEXT("FTransform"), TEXT("FVector2D"), TEXT("AActor"), TEXT("int32"), TEXT("USaveGame")
	};                        

	const TArray<FString> AllIdentifierConstants = {
		TEXT("if"), TEXT("else"), TEXT("for"), TEXT("while"), TEXT("do"), TEXT("switch"), TEXT("return"), TEXT("goto")
	};

	const TArray<FString> ListOfKnownFunctionSpecifiers = {                 
		TEXT("UE_LOG")     
	};

	TArray<FKB_PinTypeInformations> MathTreeWarteschlange = {};
	TArray<FString> MathTreeContent = {};
	TArray<int> MathTreeGlobalIndex = {};   
	TArray<FKB_PinTypeInformations> PurePinWarteschlange = {};
	TArray<FString> PurePinWarteschlangeContent = {};
	TArray<int> PurePinWarteschlangeGlobalIndex = {};

	int CurrentGlobalWhiteSectionsInt = 0;
	TArray<FKB_TempVarsUntilNow> PurePinWarteschlangeAllTempDeclerationsSoFarOtherVersion = {};
	TArray<FKB_TempVarsUntilNow> PurePinWarteschlangeAllTempDeclerationsSoFarOtherVersionMath = {};

	TArray<FKB_PinTypeInformations> UniversalCodeWarteschlange = {};
	TArray<FString> UniversalCodeWarteschlangeContent = {};
	TArray<FKB_TempVarsUntilNow> UniversalCodeWarteschlangeAllDeclerationsSoFar = {};

	TArray<FString> AllTempVariableNames = {};
	TArray<FKB_PinTypeInformations> AllTempVariablePins = {};
	TArray<FString> AllTempVariableTypes = {};


	TArray<int> WildcardNodesThatNeedUpdate = {};
	TArray<int> BooleanNodesThatNeedUpdate = {};

	KBL_Compiler(SKBL_MainWidget* InWidgetRef, UBlueprint* InCurrentSpawnForBlueprint);
	FString RemoveCodeBlockMarkers(const FString& InText);
};
