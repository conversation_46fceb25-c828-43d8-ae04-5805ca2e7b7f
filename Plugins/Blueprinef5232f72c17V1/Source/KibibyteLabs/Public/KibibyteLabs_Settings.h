// Copyright 2025, Kibibyte, All rights reserved

#pragma once

#include "CoreMinimal.h"
#include "Engine/DeveloperSettings.h"

#include "KibibyteLabs.h"
#include "SKBL_MainWidget.h"

#include "KibibyteLabs_Settings.generated.h"

UENUM(BlueprintType)
enum class ELLMProvider : uint8
{
    OpenAI         UMETA(DisplayName = "OpenAI"),
    Gemini         UMETA(DisplayName = "Google Gemini (Free)"),
};

UENUM(BlueprintType)
enum class EOpenAIModel : uint8
{
    GPT4o_Mini     UMETA(DisplayName = "GPT-4o mini"),
    GPT4_1         UMETA(DisplayName = "GPT-4.1"),
    GPT4_1_Mini    UMETA(DisplayName = "GPT-4.1 mini"),
    GPT4_1_Nano    UMETA(DisplayName = "GPT-4.1 nano"),
    GPT4o          UMETA(DisplayName = "GPT-4o"),
    GPT3_5_Turbo   UMETA(DisplayName = "GPT-3.5 Turbo"),
    CustomModel    UMETA(DisplayName = "Custom")
};

UENUM(BlueprintType)
enum class EGeminiModel : uint8
{
    Gemini_2_5_Flash  UMETA(DisplayName = "Gemini 2.5 Flash"),
    Gemini_2_5_Pro    UMETA(DisplayName = "Gemini 2.5 Pro"),
    Gemini_2_0_Flash    UMETA(DisplayName = "Gemini 2.0 Flash"),
    Gemini_2_0_FlashLite    UMETA(DisplayName = "Gemini 2.0 Flash-Lite"),
    Gemini_1_5_Flash    UMETA(DisplayName = "Gemini 1.5 Flash"),
    Gemini_1_5_Pro    UMETA(DisplayName = "Gemini 1.5 Pro"),
    CustomModel       UMETA(DisplayName = "Custom")
};

class SKBL_MainWidget;

UCLASS(config = EditorPerProjectUserSettings, meta = (DisplayName = "Kibibyte Labs"))
class KIBIBYTELABS_API UKibibyteLabs_Settings : public UDeveloperSettings
{
	GENERATED_BODY()
	
public:
	UKibibyteLabs_Settings();

	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;

    UPROPERTY(EditAnywhere, config, Category = "Provider")
    ELLMProvider SelectedProvider;

    UPROPERTY(EditAnywhere, config, Category = "OpenAI", meta = (EditCondition = "SelectedProvider == ELLMProvider::OpenAI", EditConditionHides, PasswordField = true))
    FString OpenAIApiKey;

    UPROPERTY(EditAnywhere, config, Category = "OpenAI", meta = (EditCondition = "SelectedProvider == ELLMProvider::OpenAI", EditConditionHides))
    EOpenAIModel OpenAIModel;

    UPROPERTY(EditAnywhere, config, Category = "OpenAI", meta = (EditCondition = "OpenAIModel == EOpenAIModel::CustomModel", EditConditionHides))
    FString CustomOpenAIModelName;

    UPROPERTY(EditAnywhere, config, Category = "Gemini", meta = (EditCondition = "SelectedProvider == ELLMProvider::Gemini", EditConditionHides, PasswordField = true))
    FString GeminiApiKey;

    UPROPERTY(EditAnywhere, config, Category = "Gemini", meta = (EditCondition = "SelectedProvider == ELLMProvider::Gemini", EditConditionHides))
    EGeminiModel GeminiModel;

    UPROPERTY(EditAnywhere, config, Category = "Gemini", meta = (EditCondition = "GeminiModel == EGeminiModel::CustomModel", EditConditionHides))
    FString CustomGeminiModelName;

	UPROPERTY(EditAnywhere, config, Category = "Chat",
		meta = (DisplayName = "Chat History Size", ClampMin = "1", ClampMax = "20", UIMin = "1", UIMax = "20",
			ToolTip = "The number of previous messages (both user and AI) to remember. Bigger history means larger prompt size and higher API costs."))
	int32 NumberOfMessagesToRemember;

    UPROPERTY(EditAnywhere, config, Category = "Chat", meta = (DisplayName = "Auto Open Chat Window", ToolTip = "Whether the chat window should automatically open at startup"))
    bool AutoOpenChatWindow;

    UPROPERTY(EditAnywhere, config, Category = "Chat", meta = (DisplayName = "Startup Chat Window Size", ToolTip = "Initial size of the chat window (X = Width, Y = Height). Default is 1100x700. Will be clamped to a minimum of 550x350 and a maximum of 1920x1080."))
    FVector2D StartupChatWindowSize;

	UPROPERTY(EditAnywhere, config, Category = "Hotkeys", meta = (DisplayName = "Hotkey for Opening/Closing KibibyteLabs (Editor Restart Required If Changed)"))
	FInputChord KibibyteStateHotkey;

	UPROPERTY(EditAnywhere, config, Category = "Hotkeys", meta = (DisplayName = "Hotkey for focusing the input box (Editor Restart Required If Changed)"))
	FInputChord KibibyteFocusHotkey;

private:
	void UpdateApiState();
};
