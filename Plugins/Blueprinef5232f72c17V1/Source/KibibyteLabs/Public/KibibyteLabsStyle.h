// Copyright 2025, Kibibyte, All rights reserved

#pragma once

#include "CoreMinimal.h"
#include "Interfaces/IPluginManager.h"
#include "Styling/SlateStyleRegistry.h"

#include "Styling/SlateStyle.h"

class FKibibyteLabsStyle
{
public:

	static void Initialize();

	static void Shutdown();

	static void ReloadTextures();

	static const ISlateStyle& Get();

	static FName GetStyleSetName();

private:

	static TSharedRef< class FSlateStyleSet > Create();

private:

	static TSharedPtr< class FSlateStyleSet > StyleInstance;
};

class FMyRichTextStyle
{
public:
	
	static void Initialize();

	static void Shutdown();

	static const ISlateStyle& Get();

private:
	static TSharedPtr<FSlateStyleSet> StyleSet;
};
