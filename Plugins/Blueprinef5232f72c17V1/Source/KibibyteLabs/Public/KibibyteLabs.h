// Copyright 2025, Kibibyte, All rights reserved

#pragma once
#pragma warning (disable : 4668)

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"

#ifdef _WIN64
#include "Windows/AllowWindowsPlatformTypes.h"
#include "Windows.h"
#include "dwmapi.h"
#include "wingdi.h"
#include "windef.h"
#include "winuser.h"
#endif
#include "Misc/FileHelper.h"
#include "Interfaces/IPluginManager.h"
#include "Misc/Paths.h"
#include "Kismet/KismetStringLibrary.h"
#include "Misc/AES.h"

#include "HAL/FileManager.h"

#include "AssetRegistry/AssetRegistryModule.h"
#include "ComponentReregisterContext.h"
#include "Factories/MaterialFactoryNew.h"

#include "Materials/Material.h"
#include "Materials/MaterialExpressionConstant.h"
#include "Materials/MaterialExpressionTextureSample.h"

#include "BlueprintFunctionNodeSpawner.h"
#include "UObject/Class.h"
#include "EdGraph/EdGraphNode.h"
#include "EdGraph/EdGraph.h"
#include "EdGraph/EdGraphPin.h"
#include "ContentBrowserModule.h"
#include "IContentBrowserSingleton.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "UObject/ReflectedTypeAccessors.h"
#include "Math/Vector.h"
#include "Misc/Timespan.h"
#include "K2Node_EditablePinBase.h"
#include "EdGraphSchema_K2.h"
#include "Kismet2/Kismet2NameValidators.h"
#include "K2Node_Event.h"
#include "K2Node_FunctionEntry.h"
#include "K2Node_CustomEvent.h"
#include "K2Node_FunctionResult.h"

#include "Widgets/SWindow.h"
#include "Framework/Application/SlateApplication.h"
#include "TimerManager.h"
#include "Blueprint/UserWidget.h"
#include "commctrl.h"
#include <vector>

#include "Styling/SlateStyle.h"

#include "SKBL_MainWidget.h"
#include "Styling/SlateStyleRegistry.h"
#include "Framework/Application/SlateApplication.h"
#include "Slate/SlateGameResources.h"
#include "Interfaces/IPluginManager.h"
#include "Styling/SlateStyleMacros.h"

#include "Interfaces/IMainFrameModule.h"
#include "LevelEditor.h"

#include "Runtime/ApplicationCore/Public/GenericPlatform/GenericApplication.h"
#include "Framework/Application/SlateApplication.h"


#include "Editor.h"
#include "BlueprintEditor.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "K2Node.h"
#include "EdGraph/EdGraph.h"
#include "EdGraphNode_Comment.h"

#include "Styling/SlateStyleRegistry.h"

#include "Editor.h"
#include "EditorSubsystem.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "BlueprintEditor.h"

#include "Interfaces/IPluginManager.h"
#include "IImageWrapperModule.h"
#include "Misc/Paths.h"
#include "Styling/SlateStyleRegistry.h"
#include "Interfaces/IPluginManager.h"
#include "Brushes/SlateImageBrush.h"
#include "Brushes/SlateImageBrush.h"
#include "Styling/SlateTypes.h"
#include "Framework/Application/SlateApplication.h"
#include "Widgets/SWindow.h"
#include "Widgets/Layout/SBorder.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/SBoxPanel.h"
#include "Widgets/SWindow.h"
#include "Widgets/Images/SImage.h"

#include "Widgets/Images/SImage.h"
#include "Interfaces/IPluginManager.h"
#include "Misc/Paths.h"

#include "HAL/PlatformTime.h"
#include "HAL/IConsoleManager.h"
#include "TickableEditorObject.h"
#include "Containers/Ticker.h"
#include "Engine/World.h"

#include "Editor.h"
#include "TimerManager.h"

#include "Widgets/SWindow.h"

class FToolBarBuilder;
class FMenuBuilder;

class SKBL_MainWidget;

class FKibibyteLabsModule : public IModuleInterface
{
public:

	bool IsModalDialogOpen();

	virtual void StartupModule() override;
	bool OnEngineTick_InitialSetup(float DeltaTime);
	virtual void ShutdownModule() override;
	void OnLevelChanged(const FString& Filename, bool bAsTemplate);
	void ShowCenterScreenMessage();

	void PluginButtonClicked();

	SKBL_MainWidget* GetMainWidget();

private:
	bool OnAutoLaunch = false;

	bool WasWindowPreviouslyAutoHidden = false;
	FIntPoint CurrentSize0 = FIntPoint();
	FIntPoint CurrentPosition0 = FIntPoint();
	FDelegateHandle PreSlateTickHandle;

	FTSTicker::FDelegateHandle EngineTickHandle_InitialSetup;       
	bool bInitialWindowSetupDone;                        

	FDelegateHandle SelectionChangedHandle;
	void OnContentBrowserSelectionChanged(const TArray<FAssetData>& SelectedAssets, bool bIsPrimaryBrowser);

	FString CurrentBlueprintPath;    
	UBlueprint* CurrentBlueprint;

	UBlueprint* KBL_GetBlueprintFromObject(UObject* Object);

	void RegisterMenus();

	void OnKibibyteLabsActionExecuted();

	UBlueprint* GetBlueprintFromNode(UEdGraphNode* Node);

	TSharedPtr<FJsonObject> GetNodeDescriptionJson(UEdGraphNode* Node);
	TSharedPtr<FJsonObject> GetDetailedNodeDescriptionJson(UEdGraphNode* Node);
	TSharedPtr<FJsonObject> GetCodeExplanationJson(const TArray<UEdGraphNode*>& Nodes);

	void OnKibibyteLabsFindIssuesActionExecuted();

	TArray<UEdGraphNode*> GetSelectedNodesInActiveBlueprintEditor() const;

	TWeakObjectPtr<UEdGraphNode> SelectedNode;

	FString RootToResourcesDir(const FString& RelativePath, const TCHAR* Extension);

	void CreateMainWindow(FVector2D OptionalSpawningPosition);

	static WNDPROC OriginalWndProc;
	static HWND TargetWindow;

	static LRESULT CALLBACK CustomWndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);

	void SetupWindowHook(HWND targetHwnd);

	void TryLaunchWindowArgument(FString InputArgument);
	void TryCauseWindowResize(FVector2D NewSize);
	void TrySetNewBlueprintReference(UBlueprint* NewBlueprint, bool IsDirectReference);

	TSharedPtr<SKBL_MainWidget> MainWidget;

	struct FKB_MWE_MonitorInfo
	{
		int Number = 0;
		FVector2D Size = FVector2D();
		FVector4 Placement = FVector4();
	};

	void CeckIfWindowAlreadyExistIfSoMoveToCenterOfScreen(bool SpawnOnCursor, FString InputArgument);

	bool KB_MWE_MonitorInformations(TArray<FKB_MWE_MonitorInfo>& MonitorInformations, int& MonitorCount);

	static BOOL KB_MWE_MonitorEnumProc(HMONITOR hMonitor, HDC hdcMonitor, LPRECT lprcMonitor, LPARAM dwData);

	TSharedRef<class SDockTab> OnSpawnPluginTab(const class FSpawnTabArgs& SpawnTabArgs);

public:
	void DestroyWindowFully();
private:
	TSharedPtr<class FUICommandList> PluginCommands;

	HWND LocalHANDLE;
	TSharedPtr<SWindow> DestroySWindowPtr;
	FVector2D CurrentWindowSize = FVector2D(1100, 700);

	static FKibibyteLabsModule* ModuleInstance;

	const int WindowRoundness = 50;
};
