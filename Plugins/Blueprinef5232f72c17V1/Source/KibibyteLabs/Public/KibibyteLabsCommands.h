// Copyright 2025, Ki<PERSON>by<PERSON>, All rights reserved

#pragma once

#include "CoreMinimal.h"
#include "Framework/Commands/Commands.h"
#include "KibibyteLabs_Settings.h"
#include "KibibyteLabsStyle.h"

class FKibibyteLabsCommands : public TCommands<FKibibyteLabsCommands>
{
public:

	FKibibyteLabsCommands() : TCommands<FKibibyteLabsCommands>(
		TEXT("KibibyteLabs"),
		NSLOCTEXT("Contexts", "KibibyteLabs", "KibibyteLabs Plugin"),
		NAME_None,
		FKibibyteLabsStyle::GetStyleSetName())
	{
	}

	virtual void RegisterCommands() override;

public:
	TSharedPtr< FUICommandInfo > NewPluginAction;
	TSharedPtr< FUICommandInfo > NewPluginActionFocus;
};