// Copyright 2025, Kibibyte, All rights reserved

using System.IO;
using UnrealBuildTool;

public class KibibyteLabs : ModuleRules
{
    public KibibyteLabs(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
                "Kismet",
                "ToolMenus",
            }
			);

		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				"Projects",
				"InputCore",
				"EditorFramework",
				"UnrealEd",
				"ToolMenus",
				"CoreUObject",
				"Engine",
				"Slate",
				"SlateCore", 
				"HTTP",
                "Json",
				"JsonUtilities",
                "LevelEditor",
                "MainFrame",
                "Kismet",
                "BlueprintGraph",
                "EditorSubsystem",
                "AssetTools",
                "AssetRegistry",
                "EditorScriptingUtilities",
                "StructViewer",
                "Blutility",
                "UMG",
                "UMGEditor",
                "Core",
                "GraphEditor",
                "KismetCompiler",
                "SlateRHIRenderer",
                "MainFrame",
                "EditorStyle",
				"OutputLog",
                "zlib",
                "DeveloperSettings",
                "ApplicationCore",
            }
			);
    }
}