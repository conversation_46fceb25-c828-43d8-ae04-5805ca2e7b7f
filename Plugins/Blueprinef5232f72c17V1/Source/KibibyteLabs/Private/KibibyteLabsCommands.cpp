// Copyright 2025, Kibibyte, All rights reserved

#include "KibibyteLabsCommands.h"

#define LOCTEXT_NAMESPACE "FKibibyteLabsModule"

void FKibibyteLabsCommands::RegisterCommands()
{
	const UKibibyteLabs_Settings* Settings = GetDefault<UKibibyteLabs_Settings>();

	UI_COMMAND(
		NewPluginAction,
		"KibibyteLabs",
		"Bring up KibibyteLabs window",
		EUserInterfaceActionType::Button,
		Settings->KibibyteStateHotkey
	);

	UI_COMMAND(
		NewPluginActionFocus,
		"KibibyteLabs",
		"Focus KibibyteLabs chat input",
		EUserInterfaceActionType::Button,
		Settings->KibibyteFocusHotkey
	);
}

#undef LOCTEXT_NAMESPACE
