// Copyright 2025, Kibibyte, All rights reserved

#include "KibibyteLabsStyle.h"
#include "Styling/SlateStyleRegistry.h"
#include "Framework/Application/SlateApplication.h"
#include "Slate/SlateGameResources.h"
#include "Interfaces/IPluginManager.h"
#include "Styling/SlateStyleMacros.h"

#define RootToContentDir Style->RootToContentDir

TSharedPtr<FSlateStyleSet> FKibibyteLabsStyle::StyleInstance = nullptr;

void FKibibyteLabsStyle::Initialize()
{
	if (!StyleInstance.IsValid())
	{
		StyleInstance = Create();
		FSlateStyleRegistry::RegisterSlateStyle(*StyleInstance);
	}
}

void FKibibyteLabsStyle::Shutdown()
{
	FSlateStyleRegistry::UnRegisterSlateStyle(*StyleInstance);
	ensure(StyleInstance.IsUnique());
	StyleInstance.Reset();
}

FName FKibibyteLabsStyle::GetStyleSetName()
{
	static FName StyleSetName(TEXT("KibibyteLabsStyle"));
	return StyleSetName;
}

const FVector2D Icon16x16(16.0f, 16.0f);
const FVector2D Icon20x20(20.0f, 20.0f);

TSharedRef< FSlateStyleSet > FKibibyteLabsStyle::Create()
{
	TSharedRef< FSlateStyleSet > Style = MakeShareable(new FSlateStyleSet("KibibyteLabsStyle"));
	Style->SetContentRoot(IPluginManager::Get().FindPlugin("KibibyteLabs")->GetBaseDir() / TEXT("Resources"));

	Style->Set("KibibyteLabs.NewPluginAction", new IMAGE_BRUSH_SVG(TEXT("MenuButtonIcon"), Icon20x20));     

	return Style;

}

void FKibibyteLabsStyle::ReloadTextures()
{
	if (FSlateApplication::IsInitialized())
	{
		FSlateApplication::Get().GetRenderer()->ReloadTextureResources();
	}
}

const ISlateStyle& FKibibyteLabsStyle::Get()
{
	return *StyleInstance;
}

TSharedPtr<FSlateStyleSet> FMyRichTextStyle::StyleSet = nullptr;

void FMyRichTextStyle::Initialize()
{
	if (StyleSet.IsValid()) return;

	static const FName StyleSetName = "KibibyteLabsRichTextStyle";

	if (FSlateStyleRegistry::FindSlateStyle(StyleSetName) != nullptr)
	{
		return;
	}

	StyleSet = MakeShareable(new FSlateStyleSet(StyleSetName));

	FString FontBoldPath = IPluginManager::Get()
		.FindPlugin(TEXT("KibibyteLabs"))
		->GetBaseDir() / TEXT("Resources/GraphicalAssets/NotoSans_Condensed-Bold.ttf");

	FString FontBold = IPluginManager::Get().FindPlugin(TEXT("KibibyteLabs"))->GetBaseDir() / "Resources/GraphicalAssets/NotoSans_Condensed-Bold.ttf";
	StyleSet->Set("NormalText", FSlateFontInfo(FontBold, 15));

	FSlateFontInfo BoldStyleFont(FontBoldPath, 10);
	StyleSet->Set("Bold", BoldStyleFont);

	FSlateStyleRegistry::RegisterSlateStyle(*StyleSet);
}

void FMyRichTextStyle::Shutdown()
{
	if (StyleSet.IsValid())
	{
		FSlateStyleRegistry::UnRegisterSlateStyle(*StyleSet);
		ensure(StyleSet.IsUnique());
		StyleSet.Reset();
	}
}

const ISlateStyle& FMyRichTextStyle::Get()
{
	return *StyleSet;
}
