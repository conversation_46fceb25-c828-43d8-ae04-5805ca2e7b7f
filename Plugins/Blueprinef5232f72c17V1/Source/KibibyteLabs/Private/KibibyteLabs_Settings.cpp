// Copyright 2025, Kibibyte, All rights reserved

#include "KibibyteLabs_Settings.h"

void UKibibyteLabs_Settings::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
    Super::PostEditChangeProperty(PropertyChangedEvent);

    if (PropertyChangedEvent.Property)
    {
        const FName PropertyName = PropertyChangedEvent.Property->GetFName();

        if (
            PropertyName == GET_MEMBER_NAME_CHECKED(UKibibyteLabs_Settings, SelectedProvider) ||
            PropertyName == GET_MEMBER_NAME_CHECKED(UKibibyteLabs_Settings, OpenAIApiKey) ||
            PropertyName == GET_MEMBER_NAME_CHECKED(UKibibyteLabs_Settings, OpenAIModel) ||
            PropertyName == GET_MEMBER_NAME_CHECKED(UKibibyteLabs_Settings, CustomOpenAIModelName) ||
            PropertyName == GET_MEMBER_NAME_CHECKED(UKibibyteLabs_Settings, GeminiApiKey) ||
            PropertyName == GET_MEMBER_NAME_CHECKED(UKibibyteLabs_Settings, GeminiModel) ||
            PropertyName == GET_MEMBER_NAME_CHECKED(UKibibyteLabs_Settings, CustomGeminiModelName) 
            )
        {
            UpdateApiState();
        }
    }
}

void UKibibyteLabs_Settings::UpdateApiState()
{
    bool bHasValidKey = true;

    switch (SelectedProvider)
    {
    case ELLMProvider::OpenAI:
        bHasValidKey = !OpenAIApiKey.IsEmpty();
        break;
    case ELLMProvider::Gemini:
        bHasValidKey = !GeminiApiKey.IsEmpty();
        break;
    default:
        bHasValidKey = true;        
        break;
    }


    if (SKBL_MainWidget* MainWidget = FModuleManager::GetModulePtr<FKibibyteLabsModule>("KibibyteLabs")->GetMainWidget())
    {
        if (MainWidget->ChatWidgetSwitcher.IsValid())
        {
            MainWidget->ChatWidgetSwitcher->SetActiveWidgetIndex(bHasValidKey ? 0 : 2);
        }
    }
}

UKibibyteLabs_Settings::UKibibyteLabs_Settings()
{
    SelectedProvider = ELLMProvider::OpenAI;
    OpenAIModel = EOpenAIModel::GPT4o_Mini;
    CustomOpenAIModelName = TEXT("gpt-4o-mini");
    GeminiModel = EGeminiModel::Gemini_2_5_Flash;
    CustomGeminiModelName = TEXT("gemini-2.5-flash-preview-04-17");
    NumberOfMessagesToRemember = 4;
    AutoOpenChatWindow = false;
    StartupChatWindowSize = FIntPoint(1100, 700);

    KibibyteStateHotkey = FInputChord(EKeys::A, EModifierKey::Control | EModifierKey::Alt);
    KibibyteFocusHotkey = FInputChord(EKeys::B, EModifierKey::Control | EModifierKey::Alt);
}