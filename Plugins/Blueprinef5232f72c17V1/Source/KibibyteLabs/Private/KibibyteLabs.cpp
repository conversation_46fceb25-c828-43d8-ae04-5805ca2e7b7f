// Copyright 2025, Kibibyte, All rights reserved

#include "KibibyteLabs.h"
#include "KibibyteLabsStyle.h"
#include "KibibyteLabsCommands.h"
#include "LevelEditor.h"
#include "Widgets/Docking/SDockTab.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Text/STextBlock.h"

#include "SlateBasics.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Input/SEditableTextBox.h"
#include "Widgets/Layout/SUniformGridPanel.h"

#include "ToolMenus.h"

#define LOCTEXT_NAMESPACE "FKibibyteLabsModule"

static const FName KibibyteLabsTabName("KibibyteLabs");

bool FKibibyteLabsModule::IsModalDialogOpen()
{
	for (const TSharedRef<SWindow>& Window : FSlateApplication::Get().GetInteractiveTopLevelWindows())
	{
		if (Window->IsModalWindow())
		{
			return true;
		}
	}
	return false;
}

void FKibibyteLabsModule::StartupModule()
{
	FEditorDelegates::OnMapOpened.AddRaw(this, &FKibibyteLabsModule::OnLevelChanged);

	PreSlateTickHandle = FSlateApplication::Get().OnPreTick().AddLambda([this](float DeltaTime)
		{
			if (IsModalDialogOpen()) {
				if (!WasWindowPreviouslyAutoHidden) {
					const char* LocalLPCSTR = TCHAR_TO_ANSI(*FString("Kibibyte Labs - Assistent"));
					HWND LocalHANDLE = FindWindowA(NULL, LocalLPCSTR);
					if (LocalHANDLE) {
						RECT LocalRECT;
						if (GetWindowRect(LocalHANDLE, &LocalRECT)) {
							CurrentSize0 = FIntPoint(LocalRECT.right - LocalRECT.left, LocalRECT.bottom - LocalRECT.top);
							CurrentPosition0 = FIntPoint(LocalRECT.left, LocalRECT.top);
						}
						SetWindowPos(LocalHANDLE, HWND_BOTTOM, -5000, -5000, 1, 1, SWP_HIDEWINDOW);
						WasWindowPreviouslyAutoHidden = true;
					}
				}
			}
			else {
				if (WasWindowPreviouslyAutoHidden) {
					const char* LocalLPCSTR = TCHAR_TO_ANSI(*FString("Kibibyte Labs - Assistent"));
					HWND LocalHANDLE2 = FindWindowA(NULL, LocalLPCSTR);
					if (LocalHANDLE2) {
						SetWindowPos(LocalHANDLE2, HWND_TOPMOST, CurrentPosition0.X, CurrentPosition0.Y, CurrentSize0.X, CurrentSize0.Y, SWP_SHOWWINDOW);
						HRGN LocalHRGN = CreateRoundRectRgn(0, 0, CurrentWindowSize.X, CurrentWindowSize.Y, 50, 50);
						SetWindowRgn(LocalHANDLE2, LocalHRGN, true);
						WasWindowPreviouslyAutoHidden = false;
					}
				}
			}
		});


	EngineTickHandle_InitialSetup = FTSTicker::GetCoreTicker().AddTicker(FTickerDelegate::CreateRaw(this, &FKibibyteLabsModule::OnEngineTick_InitialSetup));




	ModuleInstance = this;



	const FString Section = TEXT("KibibyteLabs");
	const FString Key = TEXT("HasShownDebugMessage");
	bool bHasShown = false;
	GConfig->GetBool(*Section, *Key, bHasShown, GEditorPerProjectIni);
	if (!bHasShown)
	{

		ShowCenterScreenMessage();
		GConfig->SetBool(*Section, *Key, true, GEditorPerProjectIni);
		GConfig->Flush(false, GEditorPerProjectIni);
	}


	FKibibyteLabsStyle::Initialize();
	FMyRichTextStyle::Initialize();
	FKibibyteLabsStyle::ReloadTextures();

	FKibibyteLabsCommands::Register();
	
	PluginCommands = MakeShareable(new FUICommandList);

	PluginCommands->MapAction(
		FKibibyteLabsCommands::Get().NewPluginAction,
		FExecuteAction::CreateLambda([this]() {
			this->CeckIfWindowAlreadyExistIfSoMoveToCenterOfScreen(true, FString("Hotkey Pressed"));    
			}),
		FCanExecuteAction());

	PluginCommands->MapAction(
		FKibibyteLabsCommands::Get().NewPluginActionFocus,
		FExecuteAction::CreateLambda([this]() {
			this->CeckIfWindowAlreadyExistIfSoMoveToCenterOfScreen(false, FString("Hotkey Focus"));    
			}),
		FCanExecuteAction());

	IMainFrameModule& mainFrame = FModuleManager::Get().LoadModuleChecked<IMainFrameModule>("MainFrame");
	mainFrame.GetMainFrameCommandBindings()->Append(PluginCommands.ToSharedRef());

	
	UToolMenus::RegisterStartupCallback(FSimpleMulticastDelegate::FDelegate::CreateRaw(this, &FKibibyteLabsModule::RegisterMenus));


	FContentBrowserModule& ContentBrowser = FModuleManager::LoadModuleChecked<FContentBrowserModule>("ContentBrowser");
	SelectionChangedHandle = ContentBrowser.GetOnAssetSelectionChanged().AddRaw(this, &FKibibyteLabsModule::OnContentBrowserSelectionChanged);
}

bool FKibibyteLabsModule::OnEngineTick_InitialSetup(float DeltaTime)     
{
	if (bInitialWindowSetupDone || !GEditor)
	{
		if (bInitialWindowSetupDone && EngineTickHandle_InitialSetup.IsValid())
		{
		}
		return !bInitialWindowSetupDone;         
	}

	UWorld* EditorWorld = GEditor->GetEditorWorldContext().World();

	if (EditorWorld && EditorWorld->IsInitialized() && EditorWorld->GetNetMode() == NM_Standalone)
	{

		const UKibibyteLabs_Settings* Settings = GetDefault<UKibibyteLabs_Settings>();
		if (Settings)
		{
			if (Settings->AutoOpenChatWindow)
			{
				OnAutoLaunch = true;
				CeckIfWindowAlreadyExistIfSoMoveToCenterOfScreen(false, FString());
			}
		}

		bInitialWindowSetupDone = true;
		return false;   
	}
	return true;      
}

void FKibibyteLabsModule::ShowCenterScreenMessage()
{
	TSharedRef<SWindow> Dialog = SNew(SWindow)
		.Title(FText::FromString(TEXT("Blueprint Generator - Kibibyte Labs")))
		.ClientSize(FVector2D(640, 460))
		.SupportsMinimize(false)
		.SupportsMaximize(false)
		.HasCloseButton(true)
		.IsTopmostWindow(true)
		.FocusWhenFirstShown(true);

	Dialog->SetContent(
		SNew(SBorder)
		.Padding(20)
		.VAlign(VAlign_Center)
		.HAlign(HAlign_Center)
		[
			SNew(SVerticalBox)

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 0, 0, 10)
				[
					SNew(STextBlock)
						.Text(FText::FromString(TEXT("Thank you for purchasing Kibibyte Labs")))
						.Font(FCoreStyle::GetDefaultFontStyle("Bold", 18))
						.Justification(ETextJustify::Center)
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 0, 0, 10)
				[
					SNew(STextBlock)
						.AutoWrapText(true)
						.Text(FText::FromString(TEXT("We hope this plugin helps speed up your workflow. Please take a moment to check out the documentation to get started quickly. If you're enjoying the plugin, a rating would be greatly appreciated.")))
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 0, 0, 20)
				[
					SNew(STextBlock)
						.AutoWrapText(true)
						.Text(FText::FromString(TEXT("Note: This tool is currently in beta. If you encounter any bugs or issues, please report them so I can resolve them quickly and improve the experience.")))
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 0, 0, 20)
				[
					SNew(STextBlock)
						.AutoWrapText(true)
						.Text(FText::FromString(TEXT("Note: Free Usage is possible using the Gemini API. More on that in the documentation!")))
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.HAlign(HAlign_Center)
				[
					SNew(SVerticalBox)

						+ SVerticalBox::Slot()
						.AutoHeight()
						.Padding(5)
						[
							SNew(SButton)
								.Text(FText::FromString(TEXT("Open Documentation")))
								.OnClicked_Lambda([]() {
								FPlatformProcess::LaunchURL(TEXT("https://kibibyte.gitbook.io/kibibyte-labs"), nullptr, nullptr);
								return FReply::Handled();
									})
						]

						+ SVerticalBox::Slot()
						.AutoHeight()
						.Padding(5)
						[
							SNew(SButton)
								.Text(FText::FromString(TEXT("View Roadmap & Changelog")))
								.OnClicked_Lambda([]() {
								FPlatformProcess::LaunchURL(TEXT("https://trello.com/b/viVK7l23"), nullptr, nullptr);
								return FReply::Handled();
									})
						]
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(20, 20, 0, 0)
				.HAlign(HAlign_Center)
				[
					SNew(SButton)
						.Text(FText::FromString(TEXT("Close")))
						.OnClicked_Lambda([Dialog]() {
						FSlateApplication::Get().RequestDestroyWindow(Dialog);
						return FReply::Handled();
							})
				]
		]
	);

	FSlateApplication::Get().AddWindow(Dialog);
}

void FKibibyteLabsModule::ShutdownModule()
{
	if (EngineTickHandle_InitialSetup.IsValid())
	{
		FTSTicker::GetCoreTicker().RemoveTicker(EngineTickHandle_InitialSetup);
		EngineTickHandle_InitialSetup.Reset();
	}

	FEditorDelegates::OnMapOpened.RemoveAll(this);

	if (FSlateApplication::IsInitialized())
	{
		FSlateApplication::Get().OnPreTick().Remove(PreSlateTickHandle);
	}


	UToolMenus::UnRegisterStartupCallback(this);

	UToolMenus::UnregisterOwner(this);

	FKibibyteLabsStyle::Shutdown();
	FMyRichTextStyle::Shutdown();

	FKibibyteLabsCommands::Unregister();

}

void FKibibyteLabsModule::OnLevelChanged(const FString& Filename, bool bAsTemplate)
{

	UWorld* EditorWorld = GEditor->GetEditorWorldContext().World();
	if (EditorWorld)
	{
		ULevel* CurrentLevel = EditorWorld->GetCurrentLevel();
		if (CurrentLevel)
		{
			ULevelScriptBlueprint* LevelScriptBP = Cast<ULevelScriptBlueprint>(CurrentLevel->GetLevelScriptBlueprint());
			if (LevelScriptBP)
			{
				UBlueprint* Blueprint = Cast<UBlueprint>(LevelScriptBP);
				if (Blueprint)
				{

					if (MainWidget.IsValid())
					{
						TrySetNewBlueprintReference(Blueprint, true);
						TryLaunchWindowArgument(FString(FString("Direct Blueprint Reference - " + Blueprint->GetPathName())));;
					}
				}
			}
		}
	}
}

TSharedRef<SDockTab> FKibibyteLabsModule::OnSpawnPluginTab(const FSpawnTabArgs& SpawnTabArgs)
{
	return SNew(SDockTab)
		.TabRole(ETabRole::NomadTab)
		[
			SNew(STextBlock)
				.Text(FText::FromString("Hello, Slate!"))
		];
}

void FKibibyteLabsModule::DestroyWindowFully()
{
	if (DestroySWindowPtr.IsValid())
	{
		DestroySWindowPtr->RequestDestroyWindow();	       
	}
}

void FKibibyteLabsModule::PluginButtonClicked()
{
}

SKBL_MainWidget* FKibibyteLabsModule::GetMainWidget()
{
	return MainWidget.Get();
}

void FKibibyteLabsModule::OnContentBrowserSelectionChanged(const TArray<FAssetData>& SelectedAssets, bool bIsPrimaryBrowser)
{
	if (CurrentBlueprint && !IsValid(CurrentBlueprint))
	{
		CurrentBlueprint = nullptr;
		CurrentBlueprintPath.Empty();
	}

	if (SelectedAssets.Num() == 0)
	{
		return;
	}

	bool currentBlueprintStillSelected = false;
	if (CurrentBlueprint)
	{
		for (const FAssetData& Asset : SelectedAssets)
		{
			if (Asset.GetObjectPathString() == CurrentBlueprintPath)
			{
				currentBlueprintStillSelected = true;
				break;
			}
		}
	}

	if (currentBlueprintStillSelected)
	{
		return;
	}

	for (const FAssetData& Asset : SelectedAssets)
	{
		UObject* AssetObject = Asset.GetAsset();
		if (!IsValid(AssetObject))
			continue;

		UBlueprint* BP = KBL_GetBlueprintFromObject(AssetObject);
		if (IsValid(BP) && BP != CurrentBlueprint)
		{
			CurrentBlueprint = BP;
			CurrentBlueprintPath = Asset.GetObjectPathString();
			break;
		}
	}

	if (CurrentBlueprint) {
		TryLaunchWindowArgument(FString(FString("Content Browser Blueprint Reference - " + CurrentBlueprintPath)));
		TrySetNewBlueprintReference(CurrentBlueprint, false);
	}
}

UBlueprint* FKibibyteLabsModule::KBL_GetBlueprintFromObject(UObject* Object)
{
	UBlueprint* BP = nullptr;
	if (Object != nullptr)
	{
		BP = Cast<UBlueprint>(Object);
		if (BP == nullptr)
		{
			BP = Cast<UBlueprint>(Object->GetClass()->ClassGeneratedBy);
		}
	}
	return BP;
}

void FKibibyteLabsModule::RegisterMenus()
{
	FToolMenuOwnerScoped OwnerScoped(this);

	{

	{
		static const FName StyleSetName(TEXT("MyEditorStyle"));
		TSharedRef< FSlateStyleSet > StyleSet = MakeShareable(new FSlateStyleSet("MyCustomSlateStyle"));
		const FVector2D Icon20x20(20.0f, 20.0f);
		StyleSet->Set("MyCustomIcon", new FSlateVectorImageBrush(RootToResourcesDir("MenuButtonIcon.svg", TEXT(".svg")), Icon20x20));


		{
			UToolMenu* ToolbarMenu = UToolMenus::Get()->ExtendMenu("LevelEditor.LevelEditorToolBar.ModesToolBar");
			{
				FToolMenuSection& Section = ToolbarMenu->FindOrAddSection("File");
				{
					   Section.AddEntry(FToolMenuEntry::InitToolBarButton(
						"KibibyteLabsLevelEditor",       
						FExecuteAction::CreateLambda([this]() {
							this->CeckIfWindowAlreadyExistIfSoMoveToCenterOfScreen(false, FString("Level Editor Button"));            
							}),
						LOCTEXT("KibibyteLabsLevelEditor", "KibibyteLabs"),
						LOCTEXT("KibibyteLabsLevelEditorTooltip", "Open KibibyteLabs from Level Editor"),
						FSlateIcon(FKibibyteLabsStyle::GetStyleSetName(), "KibibyteLabs.NewPluginAction")
					));
				}
			}

			UToolMenu* ToolbarMenu2 = UToolMenus::Get()->ExtendMenu("AssetEditorToolbar.CommonActions");
			{
				FToolMenuSection& Section2 = ToolbarMenu2->FindOrAddSection("CommonActions");
				{
					FToolMenuEntry& Entry2 = Section2.AddEntry(FToolMenuEntry::InitToolBarButton(
						"KibibyteLabsAssetEditor",       
						FExecuteAction::CreateLambda([this]() {
							


							FString FoundActiveAssetName = FString();

							
							TArray<TSharedRef<SWindow>> AllWindows;
							FSlateApplication::Get().GetAllVisibleWindowsOrdered(AllWindows);

							TArray<FString> ChildTypes = {
								"SOverlay",
								"SVerticalBox",
								"SVerticalBox",
								"SDockingArea",
								"SOverlay",
								"SHorizontalBox",
								"SOverlay",
								"SSplitter",
								"SDockingTabStack",
								"SVerticalBox",
								"SBorder",
								"SVerticalBox",
								"SOverlay",
								"SHorizontalBox",
								"SVerticalBox",
								"SDockingTabWell"
							};

							for (const TSharedRef<SWindow>& Window : AllWindows)
							{
								if (Window->IsVisible() && Window->IsActive())
								{
									FString WindowTitle = Window->GetTitle().ToString();

									FVector2D WindowSize = Window->GetClientSizeInScreen();
									FVector2D WindowPos = Window->GetPositionInScreen();

									FoundActiveAssetName = WindowTitle;

									if (WindowTitle.Contains(TEXT("Unreal Editor")))
									{

										TSharedPtr<SWidget> CurrentDaddyWidget = StaticCastSharedRef<SWidget>(Window);

										for (int y = 0; y < ChildTypes.Num(); y++)
										{
											if (!CurrentDaddyWidget.IsValid())
											{
												break;
											}

											FChildren* AllCurrentChildren = CurrentDaddyWidget->GetChildren();
											bool foundNextChild = false;

											for (int z = 0; z < AllCurrentChildren->Num(); z++)
											{
												TSharedPtr<SWidget> CurrentChild = AllCurrentChildren->GetChildAt(z);
												FString CurrentType = CurrentChild->GetTypeAsString();

												if (CurrentType == ChildTypes[y])
												{
													CurrentDaddyWidget = CurrentChild;
													foundNextChild = true;

													if (CurrentType == FString("SDockingTabWell"))
													{
														FChildren* AllDockingTabChildren = CurrentDaddyWidget->GetChildren();

														for (int xy = 0; xy < AllDockingTabChildren->Num(); xy++)
														{
															TSharedPtr<SWidget> CurrentDockTab = AllDockingTabChildren->GetChildAt(xy);

															if (CurrentDockTab->GetForegroundColor().GetSpecifiedColor() == FLinearColor(1.0f, 1.0f, 1.0f, 1.0f))
															{
																if (SDockTab* DockTabPtr = StaticCast<SDockTab*>(CurrentDockTab.Get()))
																{
																	FString TabLabel = DockTabPtr->GetTabLabel().ToString();
																	FoundActiveAssetName = TabLabel;
																}
															}
														}
													}
													break;
												}
											}

											if (!foundNextChild)
											{
												break;
											}
										}
									}
								}
							}


							if (GEditor == nullptr)
							{
								return;
							}

							UAssetEditorSubsystem* AssetEditorSubsystem = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>();
							if (AssetEditorSubsystem == nullptr)
							{
								return;
							}

							FString ReferenceAssetPathName = FString();

							TArray<UObject*> EditedAssets = AssetEditorSubsystem->GetAllEditedAssets();

							UBlueprint* Blueprint = nullptr;
							for (UObject* Asset : EditedAssets)
							{
								if (Asset)
								{
									FString AssetName = Asset->GetName();
									FString AssetClass = Asset->GetClass()->GetName();

									if (AssetName == FoundActiveAssetName) {
										Blueprint = Cast<UBlueprint>(Asset);
										if (Blueprint)          
										{
											ReferenceAssetPathName = Blueprint->GetPathName();
										}
									}
								}
								else
								{
								}
							}


							if (Blueprint) {
								this->CeckIfWindowAlreadyExistIfSoMoveToCenterOfScreen(false, FString("Direct Blueprint Reference - " + ReferenceAssetPathName));
								this->TrySetNewBlueprintReference(Blueprint, true);
							}
							else {
								this->CeckIfWindowAlreadyExistIfSoMoveToCenterOfScreen(false, FString());
							}
							}),
						LOCTEXT("KibibyteLabsAssetEditor", "KibibyteLabs"),
						LOCTEXT("KibibyteLabsAssetEditorTooltip", "Open KibibyteLabs from Asset Editor"),
						FSlateIcon(FKibibyteLabsStyle::GetStyleSetName(), "KibibyteLabs.NewPluginAction")
					));
					Entry2.InsertPosition.Position = EToolMenuInsertType::Before;           
					Entry2.InsertPosition.Name = NAME_None;           
				}
			}
		


		}


		UToolMenu* ToolbarMenu3 = UToolMenus::Get()->ExtendMenu("GraphEditor.GraphContextMenu.EdGraphSchema");      
		{
			FToolMenuSection& Section3 = ToolbarMenu3->AddSection("KibibyteLabs", LOCTEXT("KibibyteLabsSection", "Kibibyte Labs"));
			{
				Section3.AddMenuEntry(
					"KibibyteLabsAction",
					TAttribute<FText>::CreateLambda([this]()
						{
							int32 NodeCount = GetSelectedNodesInActiveBlueprintEditor().Num();
							return FText::FromString(NodeCount > 1 ? "Explain Selected Code" : "Explain Node");
						}),
					TAttribute<FText>::CreateLambda([this]()
						{
							int32 NodeCount = GetSelectedNodesInActiveBlueprintEditor().Num();
							return FText::FromString(NodeCount == 1 ?
								"Gets detailed information about a selected node" :
								"Analyzes the selected code and explains it");
						}),
					FSlateIcon(FKibibyteLabsStyle::GetStyleSetName(), "KibibyteLabs.NewPluginAction"),
					FUIAction(
						FExecuteAction::CreateRaw(this, &FKibibyteLabsModule::OnKibibyteLabsActionExecuted),
						FCanExecuteAction::CreateLambda([this]()
							{
								return GetSelectedNodesInActiveBlueprintEditor().Num() > 0;
							})
					)
				);

				Section3.AddMenuEntry(
					"KibibyteLabsFindIssues",
					LOCTEXT("FindIssuesLabel", "Find Issues"),
					LOCTEXT("FindIssuesTooltip", "Scans the selected nodes for potential issues."),
					FSlateIcon(FKibibyteLabsStyle::GetStyleSetName(), "KibibyteLabs.NewPluginAction"),
					FUIAction(
						FExecuteAction::CreateRaw(this, &FKibibyteLabsModule::OnKibibyteLabsFindIssuesActionExecuted),    
						FCanExecuteAction::CreateLambda([this]()
							{
								return GetSelectedNodesInActiveBlueprintEditor().Num() > 1;
							})
					),
					EUserInterfaceActionType::Button,
					NAME_None     
				);


			}
		}

		}
	}
}

void FKibibyteLabsModule::OnKibibyteLabsActionExecuted()
{
	TArray<UEdGraphNode*> SelectedNodes = GetSelectedNodesInActiveBlueprintEditor();

	if (SelectedNodes.Num() == 0)
	{
		CeckIfWindowAlreadyExistIfSoMoveToCenterOfScreen(false, FString());
		return;
	}

	TSharedPtr<FJsonObject> JsonPayload;
	FString Prefix;

	if (SelectedNodes.Num() == 1)
	{
		JsonPayload = GetNodeDescriptionJson(SelectedNodes[0]);
		Prefix = TEXT("Explain Node - ");
	}
	else
	{
		JsonPayload = GetCodeExplanationJson(SelectedNodes);
		Prefix = TEXT("Explain Code - ");
	}

	FString JsonString;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
	FJsonSerializer::Serialize(JsonPayload.ToSharedRef(), Writer);

	FString InputArgument = Prefix + JsonString;
	CeckIfWindowAlreadyExistIfSoMoveToCenterOfScreen(false, InputArgument);
}

UBlueprint* FKibibyteLabsModule::GetBlueprintFromNode(UEdGraphNode* Node)
{
	if (!Node)
	{
		return nullptr;
	}

	UEdGraph* Graph = Node->GetGraph();
	if (!Graph)
	{
		return nullptr;
	}

	UObject* Outer = Graph->GetOuter();
	while (Outer && !Outer->IsA<UBlueprint>())
	{
		Outer = Outer->GetOuter();
	}

	return Cast<UBlueprint>(Outer);
}

TSharedPtr<FJsonObject> FKibibyteLabsModule::GetNodeDescriptionJson(UEdGraphNode* Node)
{
	if (!Node) return nullptr;

	TSharedPtr<FJsonObject> NodeJson = MakeShared<FJsonObject>();
	NodeJson->SetStringField(TEXT("title"), Node->GetNodeTitle(ENodeTitleType::FullTitle).ToString());
	NodeJson->SetStringField(TEXT("class"), Node->GetClass()->GetName());
	NodeJson->SetStringField(TEXT("tooltip"), Node->GetTooltipText().ToString());

	if (UK2Node_CallFunction* CallFuncNode = Cast<UK2Node_CallFunction>(Node))
	{
		NodeJson->SetStringField(TEXT("functionName"), CallFuncNode->GetFunctionName().ToString());
	}

	TArray<TSharedPtr<FJsonValue>> InputPinsJson;
	TArray<TSharedPtr<FJsonValue>> OutputPinsJson;

	for (UEdGraphPin* Pin : Node->Pins)
	{
		if (!Pin || Pin->bHidden) continue;

		TSharedPtr<FJsonObject> PinJson = MakeShared<FJsonObject>();
		PinJson->SetStringField(TEXT("name"), Pin->PinName.ToString());
		PinJson->SetStringField(TEXT("type"), Pin->PinType.PinCategory.ToString());
		PinJson->SetStringField(TEXT("defaultValue"), Pin->GetDefaultAsString());
		PinJson->SetStringField(TEXT("direction"), (Pin->Direction == EGPD_Input) ? TEXT("input") : TEXT("output"));

		if (Pin->Direction == EGPD_Input)
		{
			InputPinsJson.Add(MakeShared<FJsonValueObject>(PinJson));
		}
		else
		{
			OutputPinsJson.Add(MakeShared<FJsonValueObject>(PinJson));
		}
	}

	TArray<TSharedPtr<FJsonValue>> AllPinsJson;
	AllPinsJson.Append(InputPinsJson);
	AllPinsJson.Append(OutputPinsJson);
	NodeJson->SetArrayField(TEXT("pins"), AllPinsJson);

	return NodeJson;
}

TSharedPtr<FJsonObject> FKibibyteLabsModule::GetDetailedNodeDescriptionJson(UEdGraphNode* Node)
{
	if (!Node) return nullptr;

	TSharedPtr<FJsonObject> NodeJson = MakeShared<FJsonObject>();

	NodeJson->SetStringField(TEXT("title"), Node->GetNodeTitle(ENodeTitleType::FullTitle).ToString());
	NodeJson->SetStringField(TEXT("class"), Node->GetClass()->GetName());
	NodeJson->SetStringField(TEXT("tooltip"), Node->GetTooltipText().ToString());
	NodeJson->SetStringField(TEXT("guid"), Node->NodeGuid.ToString());

	if (UK2Node_CallFunction* CallFuncNode = Cast<UK2Node_CallFunction>(Node))
	{
		NodeJson->SetStringField(TEXT("functionName"), CallFuncNode->GetFunctionName().ToString());
	}

	TArray<TSharedPtr<FJsonValue>> PinsJson;

	for (UEdGraphPin* Pin : Node->Pins)
	{
		if (!Pin || Pin->bHidden) continue;

		TSharedPtr<FJsonObject> PinJson = MakeShared<FJsonObject>();
		PinJson->SetStringField(TEXT("name"), Pin->PinName.ToString());
		PinJson->SetStringField(TEXT("type"), Pin->PinType.PinCategory.ToString());
		PinJson->SetStringField(TEXT("direction"), (Pin->Direction == EGPD_Input) ? TEXT("input") : TEXT("output"));

		const bool bHasLinks = Pin->LinkedTo.Num() > 0;
		PinJson->SetBoolField(TEXT("hasConnection"), bHasLinks);

		if (bHasLinks)
		{
			TArray<TSharedPtr<FJsonValue>> LinkedPinsJson;
			for (UEdGraphPin* LinkedPin : Pin->LinkedTo)
			{
				if (!LinkedPin || LinkedPin->bHidden) continue;

				UEdGraphNode* LinkedNode = LinkedPin->GetOwningNode();
				if (!LinkedNode) continue;

				TSharedPtr<FJsonObject> LinkedInfo = MakeShared<FJsonObject>();
				LinkedInfo->SetStringField(TEXT("nodeTitle"), LinkedNode->GetNodeTitle(ENodeTitleType::FullTitle).ToString());
				LinkedInfo->SetStringField(TEXT("pinName"), LinkedPin->PinName.ToString());
				LinkedInfo->SetStringField(TEXT("nodeGuid"), LinkedNode->NodeGuid.ToString());

				LinkedPinsJson.Add(MakeShared<FJsonValueObject>(LinkedInfo));
			}
			PinJson->SetArrayField(TEXT("linkedTo"), LinkedPinsJson);
		}
		else
		{
			FString DefaultVal = Pin->GetDefaultAsString();
			PinJson->SetStringField(TEXT("defaultValue"), DefaultVal);
		}

		PinsJson.Add(MakeShared<FJsonValueObject>(PinJson));
	}

	NodeJson->SetArrayField(TEXT("pins"), PinsJson);

	return NodeJson;
}

TSharedPtr<FJsonObject> FKibibyteLabsModule::GetCodeExplanationJson(const TArray<UEdGraphNode*>& Nodes)
{
	TSharedPtr<FJsonObject> RootJson = MakeShared<FJsonObject>();

	TArray<TSharedPtr<FJsonValue>> NodeArray;

	for (UEdGraphNode* Node : Nodes)
	{
		if (!Node) continue;

		TSharedPtr<FJsonObject> NodeJson = GetDetailedNodeDescriptionJson(Node);
		if (!NodeJson.IsValid()) continue;

		NodeJson->SetStringField(TEXT("comment"), Node->NodeComment);

		NodeArray.Add(MakeShared<FJsonValueObject>(NodeJson));
	}

	RootJson->SetArrayField(TEXT("selectedNodes"), NodeArray);
	return RootJson;
}

void FKibibyteLabsModule::OnKibibyteLabsFindIssuesActionExecuted()
{
	TArray<UEdGraphNode*> SelectedNodes = GetSelectedNodesInActiveBlueprintEditor();

	if (SelectedNodes.Num() == 0)
	{
		CeckIfWindowAlreadyExistIfSoMoveToCenterOfScreen(false, FString());
		return;
	}

	TSharedPtr<FJsonObject> JsonPayload;
	FString Prefix;

	JsonPayload = GetCodeExplanationJson(SelectedNodes);
	Prefix = TEXT("Find Issues - ");

	FString JsonString;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
	FJsonSerializer::Serialize(JsonPayload.ToSharedRef(), Writer);

	FString InputArgument = Prefix + JsonString;
	CeckIfWindowAlreadyExistIfSoMoveToCenterOfScreen(false, InputArgument);
}

TArray<UEdGraphNode*> FKibibyteLabsModule::GetSelectedNodesInActiveBlueprintEditor() const
{
	TArray<UEdGraphNode*> SelectedNodes;

	TArray<UObject*> EditedAssets = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->GetAllEditedAssets();

	for (UObject* Asset : EditedAssets)
	{
		IAssetEditorInstance* AssetEditorInstance = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->FindEditorForAsset(Asset, false);
		FAssetEditorToolkit* AssetEditorToolkit = static_cast<FAssetEditorToolkit*>(AssetEditorInstance);

		TSharedPtr<SDockTab> Tab = AssetEditorToolkit->GetTabManager()->GetOwnerTab();
		if (Tab->IsForeground())
		{
			FBlueprintEditor* BlueprintEditor = static_cast<FBlueprintEditor*>(AssetEditorToolkit);
			FGraphPanelSelectionSet SelectedNodeSet = BlueprintEditor->GetSelectedNodes();

			for (FGraphPanelSelectionSet::TIterator It(SelectedNodeSet); It; ++It)
			{
				if (UEdGraphNode* Node = Cast<UEdGraphNode>(*It))
				{
					SelectedNodes.Add(Node);
				}
			}

			break;            
		}
	}

	return SelectedNodes;
}



FString FKibibyteLabsModule::RootToResourcesDir(const FString& RelativePath, const TCHAR* Extension)
{
	static const FString ContentDir = IPluginManager::Get().FindPlugin(TEXT("KibibyteLabs"))->GetBaseDir() / TEXT("Resources");
	return (ContentDir / RelativePath) + Extension;
}

void FKibibyteLabsModule::CreateMainWindow(FVector2D OptionalSpawningPosition)
{
	HWND EditorWindowHandle = GetForegroundWindow();
	if (OnAutoLaunch) {
		DWORD currentProcessID = GetCurrentProcessId();
		FString desiredSuffix = TEXT("- Unreal Editor");
		HWND hwndCandidate = NULL;

		hwndCandidate = GetTopWindow(NULL);    

		while (hwndCandidate != NULL)
		{
			if (IsWindowVisible(hwndCandidate) && GetWindow(hwndCandidate, GW_OWNER) == NULL)
			{
				DWORD windowProcessID;
				GetWindowThreadProcessId(hwndCandidate, &windowProcessID);

				if (windowProcessID == currentProcessID)
				{
					const int TitleLength = 256;
					TCHAR WindowTitle[TitleLength];
					if (GetWindowText(hwndCandidate, WindowTitle, TitleLength))
					{
						FString TitleStr(WindowTitle);
						if (TitleStr.EndsWith(desiredSuffix))
						{
							EditorWindowHandle = hwndCandidate;   
							break;   
						}
					}
				}
			}
			hwndCandidate = GetNextWindow(hwndCandidate, GW_HWNDNEXT);
		}
	}

	const UKibibyteLabs_Settings* Settings = GetDefault<UKibibyteLabs_Settings>();

	FVector2D InitialClientSize = FVector2D(1100, 700);
	if (Settings)
	{
		InitialClientSize = Settings->StartupChatWindowSize;
		InitialClientSize.X = FMath::Clamp(InitialClientSize.X, 550, 1920);
		InitialClientSize.Y = FMath::Clamp(InitialClientSize.Y, 350, 1080);
	}

	TSharedRef<SWindow> LocalSWindowRef = SNew(SWindow)
		.Title(FText::FromString(FString("Kibibyte Labs - Assistent")))
		.ClientSize(InitialClientSize)
		.bDragAnywhere(true)
		.FocusWhenFirstShown(true)
		.HasCloseButton(false)
		.SupportsMaximize(false)
		.SupportsMinimize(false)
		.UseOSWindowBorder(false)
		.LayoutBorder(FMargin(0))
		.SizingRule(ESizingRule::UserSized)
		.MinWidth(550)
		.MinHeight(350)
		.MaxWidth(1920)
		.MaxHeight(1080)
		.IsTopmostWindow(true)
		.CreateTitleBar(false);

	MainWidget = SNew(SKBL_MainWidget);
	LocalSWindowRef->SetContent(MainWidget.ToSharedRef());
	FSlateApplication::Get().AddWindow(LocalSWindowRef);

	LocalHANDLE = reinterpret_cast<HWND>(LocalSWindowRef->GetNativeWindow()->GetOSWindowHandle());
	DestroySWindowPtr = LocalSWindowRef;

	if (EditorWindowHandle) {
		MainWidget->EditorHandle = EditorWindowHandle;
	}

	SetupWindowHook(LocalHANDLE);

	RECT rect;
	GetWindowRect(LocalHANDLE, &rect);

	HRGN LocalHRGN = CreateRoundRectRgn(0, 0, rect.right - rect.left, rect.bottom - rect.top, 50, 50);
	SetWindowRgn(LocalHANDLE, LocalHRGN, true);

	if (OptionalSpawningPosition != FVector2D(0.0f, 0.0f)) {
		MoveWindow(LocalHANDLE, OptionalSpawningPosition.X, OptionalSpawningPosition.Y, rect.right - rect.left, rect.bottom - rect.top, true);
	}

	RECT LocalRECT;
	if (GetWindowRect(LocalHANDLE, &LocalRECT)) {
		FVector2D CurrentPosition = FIntPoint(LocalRECT.left, LocalRECT.top);
		SetWindowPos(LocalHANDLE, HWND_TOP, CurrentPosition.X, CurrentPosition.Y, rect.right - rect.left, rect.bottom - rect.top, SW_NORMAL);
	}
}

WNDPROC FKibibyteLabsModule::OriginalWndProc = nullptr;
HWND FKibibyteLabsModule::TargetWindow = nullptr;
FKibibyteLabsModule* FKibibyteLabsModule::ModuleInstance = nullptr;

LRESULT CALLBACK FKibibyteLabsModule::CustomWndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
	if (msg == WM_DPICHANGED || msg == WM_EXITSIZEMOVE) {
		RECT rect;
		GetWindowRect(hwnd, &rect);
		FVector2D WindowSize = FVector2D(rect.right - rect.left, rect.bottom - rect.top);
		WindowSize.X = FMath::Clamp(WindowSize.X, 550, 1920);
		WindowSize.Y = FMath::Clamp(WindowSize.Y, 350, 1080);

		HRGN LocalHRGN = CreateRoundRectRgn(0, 0, WindowSize.X, WindowSize.Y, 50, 50);
		SetWindowRgn(hwnd, LocalHRGN, TRUE);
		SetWindowPos(hwnd, HWND_TOPMOST, rect.left, rect.top, WindowSize.X, WindowSize.Y, SW_NORMAL);
		if (ModuleInstance)
		{
			ModuleInstance->CurrentWindowSize = WindowSize;
			ModuleInstance->TryCauseWindowResize(WindowSize);
		}
	}
	return CallWindowProc(OriginalWndProc, hwnd, msg, wParam, lParam);
}

void FKibibyteLabsModule::SetupWindowHook(HWND targetHwnd) {
	TargetWindow = targetHwnd;
	OriginalWndProc = (WNDPROC)SetWindowLongPtr(targetHwnd, GWLP_WNDPROC, (LONG_PTR)CustomWndProc);
}

void FKibibyteLabsModule::TryLaunchWindowArgument(FString InputArgument)
{
	if (MainWidget) {
		if (InputArgument != FString())
			MainWidget->LaunchNewInputArgument(InputArgument);
	}
}

void FKibibyteLabsModule::TryCauseWindowResize(FVector2D NewSize)
{
	if (MainWidget) {
		MainWidget->OnWindowSizeChanged(NewSize);
	}
}

void FKibibyteLabsModule::TrySetNewBlueprintReference(UBlueprint* NewBlueprint, bool IsDirectReference)
{
	if (MainWidget) {
		if (NewBlueprint)
			MainWidget->SetNewBlueprintReference(NewBlueprint, IsDirectReference);
	}
}

void FKibibyteLabsModule::CeckIfWindowAlreadyExistIfSoMoveToCenterOfScreen(bool SpawnOnCursor, FString InputArgument)
{
	FVector2D MousePosition = FVector2D(0, 0);
	if (SpawnOnCursor) {
		MousePosition = FSlateApplication::Get().GetCursorPos();
	}
	const char* LocalLPCSTR = TCHAR_TO_ANSI(*FString("Kibibyte Labs - Assistent"));
	HWND LocalHANDLE2 = FindWindowA(NULL, LocalLPCSTR);
	if (LocalHANDLE2 && MainWidget) {
		if (MainWidget->CurrentlyClosed) {
			SetWindowPos(LocalHANDLE2, HWND_TOPMOST, MainWidget->CurrentPosition.X, MainWidget->CurrentPosition.Y, CurrentWindowSize.X, CurrentWindowSize.Y, SWP_SHOWWINDOW);
			HRGN LocalHRGN = CreateRoundRectRgn(0, 0, CurrentWindowSize.X, CurrentWindowSize.Y, 50, 50);
			SetWindowRgn(LocalHANDLE2, LocalHRGN, true);
			MainWidget->CurrentlyClosed = false;
			if (SpawnOnCursor)
				MoveWindow(LocalHANDLE2, MousePosition.X, MousePosition.Y, CurrentWindowSize.X, CurrentWindowSize.Y, true);
			MainWidget->RebuildVisibleMessages();
			TryLaunchWindowArgument(InputArgument);
		}
		else {
			int64 WindowID = (int64)LocalHANDLE2;
			if (WindowID != 0 && LocalHANDLE2) {
				if (SpawnOnCursor) {
					MoveWindow(LocalHANDLE2, MousePosition.X, MousePosition.Y, CurrentWindowSize.X, CurrentWindowSize.Y, true);
				}
				else {
					if (InputArgument.Contains(FString("Direct Blueprint Reference - ")) || InputArgument.Contains(FString("Explain Node - ")) || InputArgument.Contains(FString("Explain Code - ")) || InputArgument.Contains(FString("Find Issues - "))) {
						TryLaunchWindowArgument(InputArgument);        
					}
					else {
						RECT LocalRECT;
						if (GetWindowRect((HWND)WindowID, &LocalRECT)) {
							int WindowWidth = LocalRECT.right - LocalRECT.left;
							int WidowHeight = LocalRECT.bottom - LocalRECT.top;
							TArray<FKB_MWE_MonitorInfo> LocalMonitorInformations;
							int LocalMonitorCount = 1;
							KB_MWE_MonitorInformations(LocalMonitorInformations, LocalMonitorCount);
							if (!InputArgument.Contains(FString("Hotkey Focus")))
								MoveWindow((HWND)WindowID, LocalMonitorInformations[0].Size.X / 2 - WindowWidth / 2, LocalMonitorInformations[0].Size.Y / 2 - WidowHeight / 2, WindowWidth, WidowHeight, true);
							TryLaunchWindowArgument(InputArgument);
						}
					}
				}
			}
		}
	}
	else {
		if (!SpawnOnCursor) {
			int64 WindowID = (int64)LocalHANDLE;
			if (WindowID != 0 && LocalHANDLE) {
				RECT LocalRECT;
				if (GetWindowRect((HWND)WindowID, &LocalRECT)) {
					int WindowWidth = LocalRECT.right - LocalRECT.left;
					int WidowHeight = LocalRECT.bottom - LocalRECT.top;
					TArray<FKB_MWE_MonitorInfo> LocalMonitorInformations;
					int LocalMonitorCount = 1;
					KB_MWE_MonitorInformations(LocalMonitorInformations, LocalMonitorCount);
					MousePosition = FVector2D(LocalMonitorInformations[0].Size.X / 2 - WindowWidth / 2, LocalMonitorInformations[0].Size.Y / 2 - WidowHeight / 2);
				}
			}
		}
		CreateMainWindow(MousePosition);
		TryLaunchWindowArgument(InputArgument);
	}
}

struct LocalMonitorInfoStruct {
	int MonitorCount;
	FVector2D MonitorSize;
	FVector4 MonitorPlacement;
};

bool FKibibyteLabsModule::KB_MWE_MonitorInformations(TArray<FKB_MWE_MonitorInfo>& MonitorInformations, int& MonitorCount)
{
	TArray<FKB_MWE_MonitorInfo> LocalMonitorInformations;
	FKB_MWE_MonitorInfo LocalSingleMonitorInformation;
	std::vector<LocalMonitorInfoStruct> monitorInformations;
	if (EnumDisplayMonitors(NULL, NULL, KB_MWE_MonitorEnumProc, reinterpret_cast<LPARAM>(&monitorInformations))) {
		for (const LocalMonitorInfoStruct& oneMonitor : monitorInformations) {
			LocalSingleMonitorInformation.Number = monitorInformations.size() - oneMonitor.MonitorCount;
			LocalSingleMonitorInformation.Size = oneMonitor.MonitorSize;
			LocalSingleMonitorInformation.Placement = oneMonitor.MonitorPlacement;
			LocalMonitorInformations.Add(LocalSingleMonitorInformation);
		}
		Algo::Reverse(LocalMonitorInformations);
		MonitorInformations = LocalMonitorInformations;
		MonitorCount = LocalMonitorInformations.Num();
		return true;
	}
	return false;
}

BOOL CALLBACK FKibibyteLabsModule::KB_MWE_MonitorEnumProc(HMONITOR hMonitor, HDC hdcMonitor, LPRECT lprcMonitor, LPARAM dwData)
{
	std::vector<LocalMonitorInfoStruct>* monitorInfoList = reinterpret_cast<std::vector<LocalMonitorInfoStruct>*>(dwData);
	LocalMonitorInfoStruct LocalMonitorInfos;
	LocalMonitorInfos.MonitorCount = static_cast<int>(monitorInfoList->size());
	LocalMonitorInfos.MonitorSize = FVector2D(lprcMonitor->right - lprcMonitor->left, lprcMonitor->bottom - lprcMonitor->top);
	LocalMonitorInfos.MonitorPlacement = FVector4(lprcMonitor->left, lprcMonitor->top, lprcMonitor->right, lprcMonitor->bottom);
	monitorInfoList->push_back(LocalMonitorInfos);
	return TRUE;
}

#undef LOCTEXT_NAMESPACE
	
IMPLEMENT_MODULE(FKibibyteLabsModule, KibibyteLabs)