Flite is an open source text to speech library which we use to synthesize speech audio data from text. We convert the PCM audio data from Flite to float in UE for our audio engine to use.

The big features that we use are the following: 
1. Audio streaming - we use a callback from Flite to stream in audio data as it is synthesized to minimize latency.
2. Duration stretch - We use the "duration_stretch" feature to increase and decrease the speech rate 

Changes:
There are no changes to the original source code of Flite. 
Additional files have been added, and the library has been outfitted with cmake. 
The convenience script setup_voicelist_and_langlist.sh generates the files that would have generated by the library's original build scripts.
flite.v11.suo has been removed for version control purposes.  

Building The Library: 
DO NOT USE THE ORIGINAL BUILD SCRIPTS IN THE LIBRARY!
If there is a need to regenerate the libraries, use the Buil.bat script located in Flite-<SHA>\BuildForUE\Windows.
If the automated build from RunUAT does not work, simply run the script from a VS Developer Command Prompt. 